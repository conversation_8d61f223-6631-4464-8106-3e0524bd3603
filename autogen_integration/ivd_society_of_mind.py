#!/usr/bin/env python3
"""
IVD专家Society of Mind Agent集成

使用AutoGen框架的SocietyOfMindAgent将IVD专家和评价专家封装，
实现自动化的交流和协作，并集成搜索工具函数。
"""

import sys
import json
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加路径
sys.path.append(str(Path(__file__).parent.parent))
sys.path.append(str(Path(__file__).parent.parent / "prompt_manager"))
sys.path.append(str(Path(__file__).parent.parent / "tools"))

try:
    from autogen_agentchat.agents import AssistantAgent
    from autogen_agentchat.teams import SocietyOfMindAgent, RoundRobinGroupChat
    from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
    from autogen_agentchat.ui import Console
    from autogen_ext.models.openai import OpenAIChatCompletionClient
except ImportError:
    print("AutoGen not installed. Please install with: pip install autogen-agentchat")
    sys.exit(1)

from prompt_manager import PromptManager
from regulatory_search_tools import (
    regulatory_search_tool,
    literature_search_tool,
    market_data_tool,
    get_regulation_section_tool
)


class IVDSocietyOfMind:
    """IVD专家Society of Mind Agent"""
    
    def __init__(self, model_client, prompt_store_path: str = "./ivd_prompt_store"):
        """
        初始化IVD Society of Mind Agent
        
        Args:
            model_client: AutoGen模型客户端
            prompt_store_path: prompt存储路径
        """
        self.model_client = model_client
        self.prompt_store_path = prompt_store_path
        self.pm = PromptManager(prompt_store_path)
        
        # 获取agent提示词
        self.expert_prompts = self.pm.get_prompts("ivd_expert")
        self.evaluator_prompts = self.pm.get_prompts("ivd_evaluator")
        
        # 创建内部agents
        self.ivd_expert = self._create_ivd_expert()
        self.ivd_evaluator = self._create_ivd_evaluator()
        
        # 创建Society of Mind Agent
        self.society_agent = self._create_society_agent()
    
    def _create_ivd_expert(self) -> AssistantAgent:
        """创建IVD专家Agent"""
        
        # 获取系统提示词
        system_message = self.expert_prompts['system'].format()
        
        # 添加工具使用说明
        system_message += """

## 可用工具函数

你可以使用以下工具函数来增强你的分析：

1. **regulatory_search_tool(query, regulation=None)**: 搜索法规内容
   - query: 搜索关键词
   - regulation: 特定法规名称（可选）

2. **literature_search_tool(topic)**: 搜索IVD相关文献
   - topic: 搜索主题

3. **market_data_tool(data_type, parameter=None)**: 获取市场数据
   - data_type: "market_size", "regional_data", "technology_trends"
   - parameter: 年份、地区、技术等参数

4. **get_regulation_section_tool(regulation, section)**: 获取特定法规章节
   - regulation: 法规名称
   - section: 章节名称

使用这些工具来提供更准确、更有依据的分析。在分析中引用具体的法规条款、文献证据和市场数据。
"""
        
        return AssistantAgent(
            name="IVD_Expert",
            model_client=self.model_client,
            system_message=system_message,
            tools=[
                regulatory_search_tool,
                literature_search_tool,
                market_data_tool,
                get_regulation_section_tool
            ]
        )
    
    def _create_ivd_evaluator(self) -> AssistantAgent:
        """创建IVD评价专家Agent"""
        
        # 获取系统提示词
        system_message = self.evaluator_prompts['system'].format()
        
        # 添加工具使用说明
        system_message += """

## 可用工具函数

在评价分析质量时，你可以使用以下工具来验证信息的准确性：

1. **regulatory_search_tool(query, regulation=None)**: 验证法规引用的准确性
2. **literature_search_tool(topic)**: 验证文献引用的相关性
3. **market_data_tool(data_type, parameter=None)**: 验证市场数据的准确性
4. **get_regulation_section_tool(regulation, section)**: 获取具体法规条款进行对比

使用这些工具来：
- 验证分析中引用的法规条款是否准确
- 检查市场数据是否最新和可靠
- 确认文献引用是否相关和权威
- 识别可能的信息错误或过时内容
"""
        
        return AssistantAgent(
            name="IVD_Evaluator",
            model_client=self.model_client,
            system_message=system_message,
            tools=[
                regulatory_search_tool,
                literature_search_tool,
                market_data_tool,
                get_regulation_section_tool
            ]
        )
    
    def _create_society_agent(self) -> SocietyOfMindAgent:
        """创建Society of Mind Agent"""
        
        # 创建内部团队
        inner_team = RoundRobinGroupChat(
            participants=[self.ivd_expert, self.ivd_evaluator],
            termination_condition=MaxMessageTermination(max_messages=10)
        )
        
        # Society of Mind系统提示词
        society_system_message = """
你是一个IVD（体外诊断）领域的专家咨询系统，由两个专业agent组成：

1. **IVD专家** - 提供专业的技术分析、法规指导、市场洞察和临床建议
2. **IVD评价专家** - 客观评估分析质量，识别问题并提供改进建议

## 工作流程

当收到用户问题时：
1. 首先由IVD专家进行专业分析，使用可用工具获取准确信息
2. 然后由评价专家对分析进行质量评估
3. 如果评价发现问题，专家会根据建议改进分析
4. 最终提供高质量、经过验证的专业建议

## 输出格式

请按以下格式提供最终回答：

### 专业分析
[IVD专家的分析内容]

### 质量评估
[评价专家的评估结果]

### 最终建议
[基于评估改进后的最终建议]

确保所有分析都有充分的证据支撑，引用具体的法规条款、文献和数据。
"""
        
        return SocietyOfMindAgent(
            name="IVD_Society_of_Mind",
            participants=[inner_team],
            model_client=self.model_client,
            system_message=society_system_message
        )
    
    async def analyze_question(self, question: str, question_type: str = "综合分析", 
                              context: str = "") -> str:
        """
        分析用户问题
        
        Args:
            question: 用户问题
            question_type: 问题类型
            context: 背景信息
            
        Returns:
            分析结果
        """
        
        # 构建完整的用户消息
        user_message = f"""
请对以下IVD相关问题进行专业分析：

**问题类型**: {question_type}
**具体问题**: {question}
**背景信息**: {context}

请按照以下步骤进行：
1. IVD专家首先进行专业分析，使用工具函数获取相关法规、文献和市场数据
2. 评价专家对分析进行质量评估，验证信息准确性
3. 根据评估结果提供最终的高质量建议

确保分析具有以下特征：
- 专业性：使用准确的专业术语和概念
- 深度性：提供深入的技术和商业洞察
- 数据性：基于具体数据和定量分析
- 权威性：引用可靠的法规、标准和文献
- 实用性：给出明确可操作的建议
"""
        
        # 使用Console进行交互
        console = Console()
        
        try:
            # 运行Society of Mind Agent
            result = await console.a_run(
                task=user_message,
                agent=self.society_agent,
                termination_condition=TextMentionTermination("最终建议")
            )
            
            return result.messages[-1].content if result.messages else "分析完成，但未获得结果。"
            
        except Exception as e:
            return f"分析过程中出现错误: {str(e)}"
    
    def analyze_question_sync(self, question: str, question_type: str = "综合分析", 
                             context: str = "") -> str:
        """同步版本的问题分析"""
        return asyncio.run(self.analyze_question(question, question_type, context))


class IVDConsultationSystem:
    """IVD咨询系统"""
    
    def __init__(self, api_key: str = None, model: str = "gpt-4o"):
        """
        初始化咨询系统
        
        Args:
            api_key: OpenAI API密钥
            model: 使用的模型
        """
        if not api_key:
            raise ValueError("请提供OpenAI API密钥")
        
        # 创建模型客户端
        self.model_client = OpenAIChatCompletionClient(
            model=model,
            api_key=api_key
        )
        
        # 创建Society of Mind Agent
        self.society = IVDSocietyOfMind(self.model_client)
    
    async def consult(self, question: str, question_type: str = "综合分析", 
                     context: str = "") -> str:
        """
        进行IVD咨询
        
        Args:
            question: 咨询问题
            question_type: 问题类型
            context: 背景信息
            
        Returns:
            咨询结果
        """
        return await self.society.analyze_question(question, question_type, context)
    
    def consult_sync(self, question: str, question_type: str = "综合分析", 
                    context: str = "") -> str:
        """同步版本的咨询"""
        return asyncio.run(self.consult(question, question_type, context))


async def demo_ivd_society():
    """演示IVD Society of Mind Agent"""

    print("🧬 IVD Society of Mind Agent 演示")
    print("="*60)

    # 使用模拟的模型客户端进行演示
    try:
        from unittest.mock import Mock

        # 创建模拟的模型客户端
        mock_model_client = Mock()

        # 创建Society of Mind Agent
        society = IVDSocietyOfMind(mock_model_client)

        print("✅ IVD Society of Mind Agent 创建成功")
        print(f"   - IVD专家Agent: {society.ivd_expert.name}")
        print(f"   - IVD评价专家Agent: {society.ivd_evaluator.name}")
        print(f"   - Society Agent: {society.society_agent.name}")

        # 演示工具函数集成
        print("\n🔧 集成的工具函数:")
        for i, tool in enumerate(society.ivd_expert.tools, 1):
            print(f"   {i}. {tool.__name__}")

        print("\n📋 系统提示词预览:")
        expert_system = society.ivd_expert.system_message
        print(f"   IVD专家系统提示词长度: {len(expert_system)} 字符")
        print(f"   前200字符: {expert_system[:200]}...")

        evaluator_system = society.ivd_evaluator.system_message
        print(f"   评价专家系统提示词长度: {len(evaluator_system)} 字符")
        print(f"   前200字符: {evaluator_system[:200]}...")

        return True

    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        return False


def demo_tools():
    """演示工具函数"""

    print("🔧 工具函数演示")
    print("="*60)

    # 演示法规搜索
    print("1. 法规搜索演示")
    print("-" * 30)
    result = regulatory_search_tool("design control", "ISO 13485:2016")
    print("搜索结果:")
    print(result[:500] + "..." if len(result) > 500 else result)

    print("\n2. 市场数据演示")
    print("-" * 30)
    result = market_data_tool("market_size", "2023")
    print("市场数据:")
    print(result)

    print("\n3. 法规章节获取演示")
    print("-" * 30)
    result = get_regulation_section_tool("ISO 13485:2016", "Design and development")
    print("法规章节:")
    print(result[:300] + "..." if len(result) > 300 else result)


def demo_real_autogen():
    """演示真实的AutoGen集成（需要API密钥）"""

    print("\n🚀 真实AutoGen集成演示")
    print("="*60)

    # 检查是否有API密钥环境变量
    import os
    api_key = os.getenv("OPENAI_API_KEY")

    if not api_key:
        print("⚠️  未找到OPENAI_API_KEY环境变量")
        print("   请设置环境变量: export OPENAI_API_KEY=your_api_key")
        print("   或者在代码中直接设置API密钥")
        return False

    try:
        # 创建真实的咨询系统
        consultation_system = IVDConsultationSystem(api_key)

        # 示例咨询
        question = "请分析qPCR和dPCR技术在液体活检中的应用优势"
        question_type = "技术分析"
        context = "用于肿瘤早期诊断，目标检测限0.1% VAF"

        print(f"咨询问题: {question}")
        print(f"问题类型: {question_type}")
        print(f"背景信息: {context}")
        print("\n正在分析...")

        # 进行同步咨询
        result = consultation_system.consult_sync(question, question_type, context)

        print("\n" + "="*60)
        print("咨询结果:")
        print("="*60)
        print(result)

        return True

    except Exception as e:
        print(f"真实演示过程中出现错误: {e}")
        return False


if __name__ == "__main__":
    print("IVD Society of Mind Agent 集成系统")
    print("="*60)

    # 演示工具函数
    demo_tools()

    # 演示Society of Mind Agent创建
    print("\n" + "="*60)
    success = asyncio.run(demo_ivd_society())

    if success:
        print("\n✅ Society of Mind Agent 演示成功")

        # 尝试真实的AutoGen演示
        print("\n" + "="*60)
        print("尝试真实的AutoGen演示...")
        demo_real_autogen()

    print("\n" + "="*60)
    print("演示完成！")
    print("="*60)
