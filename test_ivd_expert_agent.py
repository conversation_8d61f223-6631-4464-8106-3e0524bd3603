#!/usr/bin/env python3
"""
测试IVD专家Agent的功能

这个脚本测试创建的IVD专家agent的各种功能，
验证其专业性、深度分析能力和数据思维。
"""

import sys
from pathlib import Path

# 添加prompt_manager到Python路径
sys.path.append(str(Path(__file__).parent / "prompt_manager"))

from prompt_manager import PromptManager


def test_agent_prompts():
    """测试agent的提示词生成功能"""
    
    print("=== 测试IVD专家Agent提示词生成 ===\n")
    
    try:
        # 初始化PromptManager
        pm = PromptManager("./ivd_prompt_store")
        
        # 获取agent prompts
        prompts = pm.get_prompts("ivd_expert")
        
        # 测试场景1：技术分析咨询
        print("1. 技术分析咨询测试")
        print("-" * 40)
        
        system_prompt = prompts['system'].format()
        user_prompt = prompts['user'].format(
            question_type="技术分析",
            user_question="请详细分析qPCR和dPCR技术的原理差异、性能特点、适用场景和成本考量，并给出在液体活检应用中的选择建议。",
            context_info="我们公司正在开发一款用于肿瘤早期筛查的液体活检产品，需要选择合适的核酸检测技术平台。目标检测限为0.1% VAF，样本通量为每天100-200个样本。"
        )
        
        print("系统提示词（前500字符）:")
        print(system_prompt[:500] + "...\n")
        
        print("用户提示词:")
        print(user_prompt)
        print("\n" + "="*60 + "\n")
        
        # 测试场景2：法规咨询
        print("2. 法规咨询测试")
        print("-" * 40)
        
        user_prompt_2 = prompts['user'].format(
            question_type="法规合规性",
            user_question="我们的新型POCT产品准备申请FDA 510(k)，请分析申请流程、技术要求、临床试验设计要点和可能的风险点。",
            context_info="产品是基于侧向流免疫技术的心肌标志物检测试剂盒，检测cTnI，预期用于急诊科和ICU。与市场上已有的Abbott i-STAT cTnI产品进行对比。"
        )
        
        print("用户提示词:")
        print(user_prompt_2)
        print("\n" + "="*60 + "\n")
        
        # 测试场景3：市场分析
        print("3. 市场分析测试")
        print("-" * 40)
        
        user_prompt_3 = prompts['user'].format(
            question_type="市场和商业分析",
            user_question="请分析全球NGS市场的竞争格局、技术趋势和投资机会，特别关注临床应用领域的发展前景。",
            context_info="我们是一家投资机构，正在评估NGS相关公司的投资价值，需要了解市场规模、增长驱动因素、主要参与者和技术壁垒。"
        )
        
        print("用户提示词:")
        print(user_prompt_3)
        print("\n" + "="*60 + "\n")
        
        # 测试场景4：临床应用咨询
        print("4. 临床应用咨询测试")
        print("-" * 40)
        
        user_prompt_4 = prompts['user'].format(
            question_type="临床应用价值",
            user_question="请评估液体活检技术在肺癌早期诊断中的临床价值，包括诊断性能、成本效益和实施挑战。",
            context_info="某三甲医院肿瘤科正在考虑引入液体活检技术用于肺癌筛查，需要评估其临床实用性和经济效益。目标人群为50岁以上高危人群，预计年检测量5000例。"
        )
        
        print("用户提示词:")
        print(user_prompt_4)
        print("\n" + "="*60 + "\n")
        
        print("=== 所有测试场景生成成功！ ===")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_knowledge_modules():
    """测试知识模块的内容"""
    
    print("=== 测试知识模块内容 ===\n")
    
    try:
        # 读取各个知识模块文件
        knowledge_files = [
            ("技术知识库", "ivd_prompt_store/ivd_expert/technical_knowledge.md"),
            ("法规知识库", "ivd_prompt_store/ivd_expert/regulatory_knowledge.md"),
            ("市场知识库", "ivd_prompt_store/ivd_expert/market_knowledge.md"),
            ("临床知识库", "ivd_prompt_store/ivd_expert/clinical_knowledge.md")
        ]
        
        for name, file_path in knowledge_files:
            print(f"{name}:")
            print("-" * 30)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                print(f"文件大小: {len(content)} 字符")
                print(f"行数: {len(content.splitlines())} 行")
                
                # 显示前200字符作为预览
                print(f"内容预览:")
                print(content[:200] + "...")
                print()
                
            except FileNotFoundError:
                print(f"错误：文件 {file_path} 不存在")
            except Exception as e:
                print(f"错误：读取文件 {file_path} 失败: {e}")
        
        print("=== 知识模块测试完成 ===")
        return True
        
    except Exception as e:
        print(f"知识模块测试失败: {e}")
        return False


def test_agent_configuration():
    """测试agent配置"""
    
    print("=== 测试Agent配置 ===\n")
    
    try:
        pm = PromptManager("./ivd_prompt_store")
        
        # 获取agent信息
        info = pm.get_agent_info("ivd_expert")
        
        print("Agent基本信息:")
        print(f"  名称: {info['name']}")
        print(f"  目录: {info['directory']}")
        print(f"  变量数量: {len(info['variables'])}")
        print(f"  系统模板大小: {info['system_template_size']} 字节")
        print(f"  用户模板大小: {info['user_template_size']} 字节")
        print()
        
        print("知识模块变量:")
        for var in info['variables']:
            print(f"  - {var}")
        print()
        
        # 获取模板信息
        prompts = pm.get_prompts("ivd_expert")
        
        print("模板变量信息:")
        print(f"  系统模板变量: {prompts['system'].input_variables}")
        print(f"  用户模板变量: {prompts['user'].input_variables}")
        print()
        
        # 检查配置文件
        import json
        config_path = Path(info['config_file'])
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("配置文件内容:")
        print(json.dumps(config, indent=2, ensure_ascii=False))
        
        print("\n=== Agent配置测试完成 ===")
        return True
        
    except Exception as e:
        print(f"配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    
    print("开始测试IVD专家Agent...\n")
    
    # 运行所有测试
    tests = [
        ("Agent配置测试", test_agent_configuration),
        ("知识模块测试", test_knowledge_modules),
        ("提示词生成测试", test_agent_prompts)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"运行 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{test_name} {'通过' if result else '失败'}\n")
        except Exception as e:
            print(f"{test_name} 异常: {e}\n")
            results.append((test_name, False))
    
    # 总结测试结果
    print("="*60)
    print("测试结果总结:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！IVD专家Agent已准备就绪。")
        print("\n使用说明:")
        print("1. 导入prompt_manager模块")
        print("2. 初始化PromptManager('./ivd_prompt_store')")
        print("3. 使用get_prompts('ivd_expert')获取提示词模板")
        print("4. 根据需要格式化用户提示词并发送给AI模型")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查配置。")


if __name__ == "__main__":
    main()
