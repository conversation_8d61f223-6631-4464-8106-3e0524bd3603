#!/usr/bin/env python3
"""
更新IVD专家和评价专家Agent，集成法规知识库和搜索工具

这个脚本将法规数据库和搜索工具集成到现有的agent中，
增强其专业能力和数据支撑。
"""

import sys
from pathlib import Path

# 添加prompt_manager到Python路径
sys.path.append(str(Path(__file__).parent / "prompt_manager"))

from prompt_manager import PromptManager


def update_ivd_expert_with_regulations():
    """更新IVD专家agent，集成法规知识库"""
    
    print("🔬 更新IVD专家Agent")
    print("="*50)
    
    pm = PromptManager("./ivd_prompt_store")
    
    # 加载现有的系统提示词
    prompts = pm.get_prompts("ivd_expert")
    current_system = prompts['system'].format()
    
    # 添加法规知识库和工具说明
    regulatory_enhancement = """

## 集成法规知识库

你现在可以访问完整的法规知识库，包括：

### 核心法规标准
1. **ISO 13485:2016** - 医疗器械质量管理体系
   - 4. 质量管理体系要求
   - 5. 管理职责
   - 6. 资源管理  
   - 7. 产品实现
   - 8. 测量、分析和改进

2. **FDA 21 CFR Part 820** - 美国医疗器械质量体系法规
   - Subpart B: 质量体系要求
   - Subpart C: 设计控制
   - Subpart F: 标识和可追溯性
   - Subpart G: 生产和过程控制
   - Subpart J: 纠正和预防措施

3. **EU IVDR 2017/746** - 欧盟体外诊断医疗器械法规
   - 风险分类系统 (A/B/C/D类)
   - 性能评估要求
   - 公告机构参与
   - 唯一器械标识 (UDI)
   - 上市后监督义务

### 可用工具函数

在分析中使用以下工具获取准确信息：

1. **regulatory_search_tool(query, regulation=None)**
   - 搜索特定法规内容
   - 示例: regulatory_search_tool("design control", "ISO 13485:2016")

2. **get_regulation_section_tool(regulation, section)**
   - 获取特定法规章节
   - 示例: get_regulation_section_tool("FDA 21 CFR Part 820", "Design controls")

3. **market_data_tool(data_type, parameter=None)**
   - 获取IVD市场数据
   - 示例: market_data_tool("market_size", "2023")

4. **literature_search_tool(topic)**
   - 搜索相关文献
   - 示例: literature_search_tool("qPCR validation")

### 分析要求增强

在提供分析时，必须：
1. **引用具体法规条款** - 使用工具获取准确的法规条文
2. **提供最新市场数据** - 使用市场数据工具获取当前数据
3. **验证技术信息** - 通过文献搜索验证技术声明
4. **考虑多地区法规** - 分析不同市场的法规差异

### 示例分析流程

对于法规相关问题：
1. 使用regulatory_search_tool搜索相关条款
2. 使用get_regulation_section_tool获取具体章节
3. 对比不同法规的要求差异
4. 提供具体的合规建议

对于市场分析问题：
1. 使用market_data_tool获取最新市场规模
2. 分析技术细分市场趋势
3. 结合法规环境评估市场机会
4. 提供数据支撑的商业建议
"""
    
    # 更新系统提示词
    enhanced_system = current_system + regulatory_enhancement
    
    # 重新注册agent
    pm.delete_agent("ivd_expert")
    
    # 加载其他知识模块
    with open("ivd_technical_knowledge.md", 'r', encoding='utf-8') as f:
        technical_knowledge = f.read()
    
    with open("ivd_regulatory_knowledge.md", 'r', encoding='utf-8') as f:
        regulatory_knowledge = f.read()
    
    # 加载法规数据库内容
    regulatory_database = ""
    for reg_file in ["regulatory_database/iso_13485_2016.md", 
                     "regulatory_database/fda_21_cfr_part_820.md",
                     "regulatory_database/eu_ivdr_2017_746.md"]:
        try:
            with open(reg_file, 'r', encoding='utf-8') as f:
                regulatory_database += f"\n\n{f.read()}"
        except FileNotFoundError:
            print(f"警告：未找到文件 {reg_file}")
    
    # 创建市场知识模块
    market_knowledge = """# IVD市场分析知识库

## 全球市场概况
- **2023年市场规模**: $102.1亿美元
- **预期CAGR (2023-2030)**: 6.1%
- **2030年预测规模**: $154.3亿美元

## 技术细分市场
- **分子诊断**: 25%份额，8.5%增长率
- **免疫检测**: 35%份额，5.2%增长率
- **生化诊断**: 20%份额，4.8%增长率
- **血液学**: 10%份额，4.2%增长率

## 区域市场分布
- **北美**: 40%份额，5.8%增长率
- **欧洲**: 30%份额，5.5%增长率
- **亚太**: 25%份额，8.2%增长率
- **其他**: 5%份额，4.5%增长率"""
    
    # 创建临床知识模块
    clinical_knowledge = """# IVD临床应用知识库

## 疾病诊断应用
### 感染性疾病
- **新冠检测**: RT-PCR敏感性95-99%，抗原检测80-95%
- **细菌感染**: PCT正常值<0.25 ng/mL，CRP正常值<3 mg/L

### 心血管疾病
- **心肌梗死**: cTnI/cTnT 99th百分位值作为诊断切点
- **心力衰竭**: NT-proBNP <125 pg/mL排除心衰

### 肿瘤标志物
- **肺癌**: CEA正常值<5 ng/mL，CYFRA21-1鳞癌标志物
- **肝癌**: AFP >400 ng/mL高度提示肝癌"""
    
    pm.register_new_agent(
        agent_name="ivd_expert",
        system_message=enhanced_system,
        user_message=prompts['user'].template,
        technical_knowledge=technical_knowledge,
        regulatory_knowledge=regulatory_knowledge,
        regulatory_database=regulatory_database,
        market_knowledge=market_knowledge,
        clinical_knowledge=clinical_knowledge
    )
    
    print("✅ IVD专家Agent更新完成")
    print(f"   - 系统提示词长度: {len(enhanced_system)} 字符")
    print(f"   - 知识模块数量: 5 个")
    print(f"   - 集成法规: ISO 13485, FDA 21 CFR Part 820, EU IVDR")


def update_ivd_evaluator_with_regulations():
    """更新IVD评价专家agent，集成法规验证工具"""
    
    print("\n📊 更新IVD评价专家Agent")
    print("="*50)
    
    pm = PromptManager("./ivd_prompt_store")
    
    # 加载现有的系统提示词
    prompts = pm.get_prompts("ivd_evaluator")
    current_system = prompts['system'].format()
    
    # 添加法规验证工具说明
    verification_enhancement = """

## 集成法规验证工具

在评价分析质量时，你现在可以使用以下工具进行信息验证：

### 法规验证工具
1. **regulatory_search_tool(query, regulation=None)**
   - 验证法规引用的准确性
   - 检查法规条款是否被正确引用

2. **get_regulation_section_tool(regulation, section)**
   - 获取具体法规条款进行对比
   - 验证分析中的法规解读是否准确

3. **market_data_tool(data_type, parameter=None)**
   - 验证市场数据的准确性和时效性
   - 检查数据来源的可靠性

4. **literature_search_tool(topic)**
   - 验证文献引用的相关性
   - 检查是否有更新的研究证据

### 验证评价框架

对于每个分析，进行以下验证：

#### 法规准确性验证
1. **条款引用验证**: 使用工具检查引用的法规条款是否准确
2. **法规理解验证**: 验证对法规要求的理解是否正确
3. **多地区法规对比**: 检查是否正确区分不同地区的法规差异
4. **法规时效性验证**: 确认引用的法规版本是否为最新

#### 数据准确性验证
1. **市场数据验证**: 使用工具验证市场规模、增长率等数据
2. **技术参数验证**: 检查技术性能参数是否符合实际
3. **统计数据验证**: 验证敏感性、特异性等统计指标
4. **时间一致性验证**: 确保数据的时间标注正确

#### 文献证据验证
1. **引用相关性**: 验证引用文献与论点的相关性
2. **证据等级**: 评估引用证据的质量等级
3. **证据平衡性**: 检查是否平衡引用支持和反对证据
4. **证据时效性**: 确认文献发表时间的合理性

### 评价报告增强

在评价报告中，必须包含：

#### 验证结果部分
- **法规验证结果**: 列出验证的法规条款和准确性
- **数据验证结果**: 说明验证的数据来源和可靠性
- **文献验证结果**: 评估引用文献的质量和相关性

#### 问题识别增强
- **法规错误**: 具体指出错误的法规引用或理解
- **数据错误**: 标明不准确或过时的数据
- **逻辑错误**: 识别推理过程中的逻辑问题

#### 改进建议具体化
- **法规改进**: 提供正确的法规条款和解读
- **数据改进**: 建议使用更准确、更新的数据源
- **方法改进**: 推荐更科学的分析方法

### 质量评分细化

基于验证结果，对以下维度进行更精确评分：

1. **法规准确性** (0-25分)
   - 法规引用准确性 (0-10分)
   - 法规理解正确性 (0-10分)
   - 法规时效性 (0-5分)

2. **数据可靠性** (0-25分)
   - 数据来源权威性 (0-10分)
   - 数据准确性 (0-10分)
   - 数据时效性 (0-5分)

3. **证据充分性** (0-25分)
   - 文献质量 (0-10分)
   - 证据相关性 (0-10分)
   - 证据平衡性 (0-5分)

4. **分析逻辑性** (0-25分)
   - 推理逻辑 (0-15分)
   - 结论支撑度 (0-10分)
"""
    
    # 更新系统提示词
    enhanced_system = current_system + verification_enhancement
    
    # 重新注册agent
    pm.delete_agent("ivd_evaluator")
    
    # 加载知识模块
    with open("ivd_evaluation_standards.md", 'r', encoding='utf-8') as f:
        evaluation_standards = f.read()
    
    with open("ivd_common_issues.md", 'r', encoding='utf-8') as f:
        common_issues = f.read()
    
    # 创建验证方法论模块
    verification_methodology = """# 法规验证方法论

## 验证流程
1. **自动验证**: 使用工具自动检查法规引用
2. **交叉验证**: 对比多个法规来源
3. **专家验证**: 结合专业知识进行判断
4. **持续验证**: 定期更新验证标准

## 验证标准
- **准确性**: 信息是否准确无误
- **完整性**: 信息是否完整全面
- **时效性**: 信息是否为最新版本
- **相关性**: 信息是否与主题相关"""
    
    # 创建验证案例模块
    verification_cases = """# 法规验证案例

## 常见验证场景
### 法规引用错误
- **错误**: 引用已废止的法规版本
- **验证**: 使用工具检查法规当前状态
- **改进**: 更新为最新版本法规

### 数据过时
- **错误**: 使用2020年的市场数据分析2024年市场
- **验证**: 检查数据发布时间
- **改进**: 使用最新的市场报告数据

### 文献不相关
- **错误**: 引用与主题无关的文献
- **验证**: 评估文献与论点的相关性
- **改进**: 选择更相关的文献支撑"""
    
    pm.register_new_agent(
        agent_name="ivd_evaluator",
        system_message=enhanced_system,
        user_message=prompts['user'].template,
        evaluation_standards=evaluation_standards,
        common_issues=common_issues,
        verification_methodology=verification_methodology,
        verification_cases=verification_cases
    )
    
    print("✅ IVD评价专家Agent更新完成")
    print(f"   - 系统提示词长度: {len(enhanced_system)} 字符")
    print(f"   - 知识模块数量: 4 个")
    print(f"   - 集成验证工具: 4 个")


def test_updated_agents():
    """测试更新后的agents"""
    
    print("\n🧪 测试更新后的Agents")
    print("="*50)
    
    pm = PromptManager("./ivd_prompt_store")
    
    # 测试IVD专家
    expert_prompts = pm.get_prompts("ivd_expert")
    expert_info = pm.get_agent_info("ivd_expert")
    
    print("IVD专家Agent:")
    print(f"   - 知识模块: {len(expert_info['variables'])} 个")
    print(f"   - 系统提示词: {expert_info['system_template_size']} 字节")
    print(f"   - 模块列表: {expert_info['variables']}")
    
    # 测试评价专家
    evaluator_prompts = pm.get_prompts("ivd_evaluator")
    evaluator_info = pm.get_agent_info("ivd_evaluator")
    
    print(f"\nIVD评价专家Agent:")
    print(f"   - 知识模块: {len(evaluator_info['variables'])} 个")
    print(f"   - 系统提示词: {evaluator_info['system_template_size']} 字节")
    print(f"   - 模块列表: {evaluator_info['variables']}")
    
    # 测试提示词生成
    print(f"\n📋 提示词生成测试:")
    
    expert_system = expert_prompts['system'].format()
    print(f"   - 专家系统提示词长度: {len(expert_system)} 字符")
    
    evaluator_system = evaluator_prompts['system'].format()
    print(f"   - 评价专家系统提示词长度: {len(evaluator_system)} 字符")
    
    return True


def main():
    """主函数"""
    
    print("🔧 更新IVD Agents集成法规知识库和搜索工具")
    print("="*70)
    
    try:
        # 更新IVD专家
        update_ivd_expert_with_regulations()
        
        # 更新评价专家
        update_ivd_evaluator_with_regulations()
        
        # 测试更新结果
        test_updated_agents()
        
        print(f"\n🎉 Agent更新完成！")
        print(f"="*70)
        print(f"✅ 两个agent已成功集成法规知识库和搜索工具")
        print(f"✅ 可以使用工具函数获取准确的法规和市场信息")
        print(f"✅ 评价专家可以验证分析中的信息准确性")
        print(f"✅ 形成了完整的质量控制和验证体系")
        
    except Exception as e:
        print(f"更新过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
