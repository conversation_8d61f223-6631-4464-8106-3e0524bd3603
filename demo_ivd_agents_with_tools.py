#!/usr/bin/env python3
"""
IVD专家和评价专家Agent集成演示

演示两个agent如何使用搜索工具函数进行协同工作，
包括法规搜索、市场数据查询等功能。
"""

import sys
import json
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent / "prompt_manager"))
sys.path.append(str(Path(__file__).parent / "tools"))

from prompt_manager import PromptManager
from tools.regulatory_search_tools import (
    regulatory_search_tool,
    literature_search_tool,
    market_data_tool,
    get_regulation_section_tool,
    RegulatorySearchTools
)


class IVDAgentWithTools:
    """带工具函数的IVD Agent"""
    
    def __init__(self, agent_name: str, prompt_store_path: str = "./ivd_prompt_store"):
        """初始化Agent"""
        self.agent_name = agent_name
        self.pm = PromptManager(prompt_store_path)
        self.prompts = self.pm.get_prompts(agent_name)
        
        # 可用工具
        self.tools = {
            "regulatory_search": regulatory_search_tool,
            "literature_search": literature_search_tool,
            "market_data": market_data_tool,
            "get_regulation_section": get_regulation_section_tool
        }
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        system_prompt = self.prompts['system'].format()
        
        # 添加工具使用说明
        tools_description = """

## 可用工具函数

你可以使用以下工具函数来增强分析：

1. **regulatory_search_tool(query, regulation=None)**: 搜索法规内容
   - 示例: regulatory_search_tool("design control", "ISO 13485:2016")

2. **market_data_tool(data_type, parameter=None)**: 获取市场数据
   - 示例: market_data_tool("market_size", "2023")

3. **get_regulation_section_tool(regulation, section)**: 获取特定法规章节
   - 示例: get_regulation_section_tool("ISO 13485:2016", "Design controls")

4. **literature_search_tool(topic)**: 搜索相关文献
   - 示例: literature_search_tool("qPCR liquid biopsy")

在分析中使用这些工具获取准确的法规信息、市场数据和文献证据。
"""
        
        return system_prompt + tools_description
    
    def generate_analysis_prompt(self, question: str, question_type: str, context: str = "") -> str:
        """生成分析提示词"""
        if self.agent_name == "ivd_expert":
            user_prompt = self.prompts['user'].format(
                question_type=question_type,
                user_question=question,
                context_info=context
            )
        else:  # ivd_evaluator
            # 这里需要分析内容，暂时用占位符
            user_prompt = self.prompts['user'].format(
                original_question_type=question_type,
                original_question=question,
                original_context=context,
                analysis_content="[待评价的分析内容]"
            )
        
        return user_prompt
    
    def use_tool(self, tool_name: str, *args, **kwargs) -> str:
        """使用工具函数"""
        if tool_name in self.tools:
            try:
                result = self.tools[tool_name](*args, **kwargs)
                return result
            except Exception as e:
                return f"工具调用错误: {str(e)}"
        else:
            return f"未知工具: {tool_name}"


class IVDCollaborationDemo:
    """IVD Agent协作演示"""
    
    def __init__(self):
        """初始化演示系统"""
        self.expert_agent = IVDAgentWithTools("ivd_expert")
        self.evaluator_agent = IVDAgentWithTools("ivd_evaluator")
        self.regulatory_tools = RegulatorySearchTools()
    
    def demo_expert_analysis(self, question: str, question_type: str, context: str = ""):
        """演示专家分析过程"""
        
        print("🔬 IVD专家分析演示")
        print("="*60)
        
        print(f"问题类型: {question_type}")
        print(f"具体问题: {question}")
        print(f"背景信息: {context}")
        
        # 生成系统提示词
        system_prompt = self.expert_agent.get_system_prompt()
        print(f"\n📋 系统提示词长度: {len(system_prompt)} 字符")
        
        # 生成用户提示词
        user_prompt = self.expert_agent.generate_analysis_prompt(question, question_type, context)
        print(f"📋 用户提示词长度: {len(user_prompt)} 字符")
        
        # 演示工具使用
        print(f"\n🔧 工具函数演示:")
        
        # 1. 搜索相关法规
        if "法规" in question or "标准" in question or "FDA" in question or "ISO" in question:
            print("   正在搜索相关法规...")
            reg_result = self.expert_agent.use_tool("regulatory_search", "quality management")
            reg_data = json.loads(reg_result)
            if reg_data:
                print(f"   找到 {len(reg_data)} 个相关法规条款")
                print(f"   最相关: {reg_data[0]['title']}")
        
        # 2. 获取市场数据
        if "市场" in question or "商业" in question or "投资" in question:
            print("   正在获取市场数据...")
            market_result = self.expert_agent.use_tool("market_data", "market_size", "2023")
            market_data = json.loads(market_result)
            print(f"   2023年全球IVD市场规模: ${market_data['size_billion_usd']}亿美元")
            print(f"   年增长率: {market_data['growth_rate']}%")
        
        # 3. 获取技术趋势
        if "技术" in question or "qPCR" in question or "NGS" in question:
            print("   正在获取技术趋势数据...")
            tech_result = self.expert_agent.use_tool("market_data", "technology_trends")
            tech_data = json.loads(tech_result)
            print(f"   分子诊断市场份额: {tech_data['molecular_diagnostics']['share_percent']}%")
            print(f"   免疫检测市场份额: {tech_data['immunoassays']['share_percent']}%")
        
        print(f"\n✅ 专家分析提示词已生成，可发送给AI模型进行分析")
        
        return {
            "system_prompt": system_prompt,
            "user_prompt": user_prompt,
            "tools_used": ["regulatory_search", "market_data"]
        }
    
    def demo_evaluator_assessment(self, analysis_content: str):
        """演示评价专家评估过程"""
        
        print("\n📊 IVD评价专家评估演示")
        print("="*60)
        
        # 生成评价提示词
        system_prompt = self.evaluator_agent.get_system_prompt()
        print(f"📋 评价专家系统提示词长度: {len(system_prompt)} 字符")
        
        # 演示评价过程中的工具使用
        print(f"\n🔧 评价过程中的工具验证:")
        
        # 验证法规引用
        print("   正在验证法规引用的准确性...")
        if "ISO 13485" in analysis_content:
            reg_result = self.evaluator_agent.use_tool("get_regulation_section", "ISO 13485:2016", "Quality management")
            print("   ✅ ISO 13485引用验证完成")
        
        # 验证市场数据
        print("   正在验证市场数据的时效性...")
        market_result = self.evaluator_agent.use_tool("market_data", "market_size", "2023")
        market_data = json.loads(market_result)
        print(f"   ✅ 市场数据验证: {market_data['size_billion_usd']}亿美元 (2023年)")
        
        print(f"\n✅ 评价专家评估提示词已生成，可进行质量评价")
        
        return {
            "system_prompt": system_prompt,
            "verification_tools": ["regulatory_verification", "market_data_verification"]
        }
    
    def demo_complete_workflow(self):
        """演示完整的协作工作流程"""
        
        print("🚀 IVD专家协作完整工作流程演示")
        print("="*80)
        
        # 示例问题
        question = "请分析qPCR和dPCR技术在液体活检中的应用，包括技术原理、性能对比、法规要求和市场前景"
        question_type = "技术分析"
        context = "我们公司正在开发液体活检产品，需要选择合适的核酸检测技术平台，目标市场为欧美"
        
        # 第一步：专家分析
        expert_result = self.demo_expert_analysis(question, question_type, context)
        
        # 模拟分析内容
        mock_analysis = """
## qPCR vs dPCR技术分析

### 技术原理对比
- **qPCR**: 基于实时荧光监测的相对定量技术
- **dPCR**: 基于样本分割和泊松分布的绝对定量技术

### 性能对比
- **检测限**: qPCR 10-100 copies/mL, dPCR 1 copy/μL
- **精密度**: qPCR CV<5%, dPCR CV<10%
- **通量**: qPCR高通量(96-384样本), dPCR中等通量

### 法规要求
根据ISO 13485:2016质量管理体系要求，需要进行充分的设计控制和验证。
FDA 510(k)路径适用于与已批准产品实质等同的器械。

### 市场前景
全球IVD市场2023年规模102.1亿美元，分子诊断占25%份额，年增长率8.5%。
        """
        
        # 第二步：评价专家评估
        evaluator_result = self.demo_evaluator_assessment(mock_analysis)
        
        print(f"\n🎯 协作工作流程总结:")
        print(f"   1. ✅ IVD专家生成专业分析 ({len(expert_result['system_prompt'])} 字符)")
        print(f"   2. ✅ 使用工具获取法规和市场数据")
        print(f"   3. ✅ 评价专家进行质量评估")
        print(f"   4. ✅ 验证分析中的关键信息")
        print(f"   5. ✅ 形成完整的质量控制闭环")
        
        return {
            "expert_analysis": expert_result,
            "evaluator_assessment": evaluator_result,
            "workflow_status": "completed"
        }


def demo_regulatory_database():
    """演示法规数据库功能"""
    
    print("📚 法规数据库演示")
    print("="*60)
    
    tools = RegulatorySearchTools()
    
    # 显示可用法规
    regulations = tools.list_available_regulations()
    print(f"可用法规: {regulations}")
    
    # 演示搜索功能
    print(f"\n🔍 搜索演示:")
    
    search_queries = [
        ("design control", "ISO 13485:2016"),
        ("quality system", "FDA 21 CFR Part 820"),
        ("risk classification", "EU IVDR 2017/746")
    ]
    
    for query, regulation in search_queries:
        print(f"\n搜索: '{query}' 在 {regulation}")
        results = tools.search_regulation(query, regulation)
        if results:
            print(f"   找到 {len(results)} 个结果")
            print(f"   最相关: {results[0].title} (相关性: {results[0].relevance_score:.1f})")
        else:
            print("   未找到相关结果")


def main():
    """主演示函数"""
    
    print("🧬 IVD专家Agent集成系统演示")
    print("="*80)
    print("本演示展示IVD专家和评价专家如何使用工具函数进行协作")
    print("="*80)
    
    # 1. 演示法规数据库
    demo_regulatory_database()
    
    # 2. 演示Agent协作
    print(f"\n" + "="*80)
    demo = IVDCollaborationDemo()
    result = demo.demo_complete_workflow()
    
    # 3. 总结
    print(f"\n" + "="*80)
    print("演示总结:")
    print("="*80)
    print("✅ 法规数据库: 包含ISO 13485、FDA 21 CFR Part 820、EU IVDR等")
    print("✅ 搜索工具: 支持法规搜索、市场数据查询、文献检索")
    print("✅ IVD专家: 集成工具函数，提供数据支撑的专业分析")
    print("✅ 评价专家: 使用工具验证信息准确性，确保质量")
    print("✅ 协作流程: 形成完整的分析→评价→改进闭环")
    
    print(f"\n💡 下一步:")
    print("   - 集成真实的AI模型 (GPT-4, Claude等)")
    print("   - 扩展法规数据库内容")
    print("   - 添加更多工具函数 (如专利搜索、竞品分析等)")
    print("   - 实现AutoGen SocietyOfMindAgent集成")


if __name__ == "__main__":
    main()
