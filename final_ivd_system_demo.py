#!/usr/bin/env python3
"""
IVD专家系统最终演示

展示完整的IVD专家和评价专家系统，包括：
1. 法规知识库 (ISO 13485, FDA 21 CFR Part 820, EU IVDR)
2. 搜索工具函数 (法规搜索、市场数据、文献检索)
3. 两个专业agent的协同工作
4. 完整的质量控制闭环
"""

import sys
import json
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent / "prompt_manager"))
sys.path.append(str(Path(__file__).parent / "tools"))

from prompt_manager import PromptManager
from tools.regulatory_search_tools import (
    regulatory_search_tool,
    market_data_tool,
    get_regulation_section_tool,
    RegulatorySearchTools
)


class IVDExpertSystem:
    """完整的IVD专家系统"""
    
    def __init__(self):
        """初始化系统"""
        self.pm = PromptManager("./ivd_prompt_store")
        self.regulatory_tools = RegulatorySearchTools()
        
        # 获取agents
        self.expert_prompts = self.pm.get_prompts("ivd_expert")
        self.evaluator_prompts = self.pm.get_prompts("ivd_evaluator")
        
        print("🧬 IVD专家系统初始化完成")
        print(f"   - 可用法规: {self.regulatory_tools.list_available_regulations()}")
        print(f"   - IVD专家知识模块: {len(self.pm.get_agent_info('ivd_expert')['variables'])} 个")
        print(f"   - 评价专家知识模块: {len(self.pm.get_agent_info('ivd_evaluator')['variables'])} 个")
    
    def analyze_with_expert(self, question: str, question_type: str, context: str = ""):
        """使用IVD专家进行分析"""
        
        print(f"\n🔬 IVD专家分析")
        print("="*60)
        print(f"问题类型: {question_type}")
        print(f"问题: {question}")
        print(f"背景: {context}")
        
        # 生成专家分析提示词
        system_prompt = self.expert_prompts['system'].format()
        user_prompt = self.expert_prompts['user'].format(
            question_type=question_type,
            user_question=question,
            context_info=context
        )
        
        print(f"\n📋 生成的提示词:")
        print(f"   - 系统提示词: {len(system_prompt)} 字符")
        print(f"   - 用户提示词: {len(user_prompt)} 字符")
        
        # 演示工具使用
        print(f"\n🔧 专家使用的工具:")
        tools_used = []
        
        # 根据问题类型使用相应工具
        if any(keyword in question.lower() for keyword in ['法规', '标准', 'iso', 'fda', 'ivdr']):
            print("   1. 搜索相关法规...")
            reg_result = regulatory_search_tool("quality management", "ISO 13485:2016")
            reg_data = json.loads(reg_result)
            if reg_data:
                print(f"      找到 {len(reg_data)} 个相关法规条款")
                tools_used.append("regulatory_search")
        
        if any(keyword in question.lower() for keyword in ['市场', '商业', '投资', '规模']):
            print("   2. 获取市场数据...")
            market_result = market_data_tool("market_size", "2023")
            market_data = json.loads(market_result)
            print(f"      2023年全球IVD市场: ${market_data['size_billion_usd']}亿美元")
            tools_used.append("market_data")
        
        if any(keyword in question.lower() for keyword in ['技术', 'qpcr', 'ngs', 'crispr']):
            print("   3. 获取技术趋势...")
            tech_result = market_data_tool("technology_trends")
            tech_data = json.loads(tech_result)
            print(f"      分子诊断市场份额: {tech_data['molecular_diagnostics']['share_percent']}%")
            tools_used.append("technology_trends")
        
        # 模拟专家分析结果
        mock_analysis = self._generate_mock_expert_analysis(question_type, question, tools_used)
        
        print(f"\n📊 专家分析结果:")
        print(f"   - 分析长度: {len(mock_analysis)} 字符")
        print(f"   - 使用工具: {', '.join(tools_used)}")
        print(f"   - 包含法规引用: {'是' if 'ISO' in mock_analysis or 'FDA' in mock_analysis else '否'}")
        print(f"   - 包含市场数据: {'是' if '亿美元' in mock_analysis or '%' in mock_analysis else '否'}")
        
        return {
            "system_prompt": system_prompt,
            "user_prompt": user_prompt,
            "analysis": mock_analysis,
            "tools_used": tools_used
        }
    
    def evaluate_with_evaluator(self, expert_result: dict, original_question: str, 
                               question_type: str, context: str = ""):
        """使用评价专家进行评估"""
        
        print(f"\n📊 IVD评价专家评估")
        print("="*60)
        
        # 生成评价提示词
        system_prompt = self.evaluator_prompts['system'].format()
        user_prompt = self.evaluator_prompts['user'].format(
            original_question_type=question_type,
            original_question=original_question,
            original_context=context,
            analysis_content=expert_result["analysis"]
        )
        
        print(f"📋 生成的评价提示词:")
        print(f"   - 系统提示词: {len(system_prompt)} 字符")
        print(f"   - 用户提示词: {len(user_prompt)} 字符")
        
        # 演示验证工具使用
        print(f"\n🔍 评价专家验证过程:")
        verification_results = []
        
        # 验证法规引用
        if "ISO 13485" in expert_result["analysis"]:
            print("   1. 验证ISO 13485引用...")
            reg_section = get_regulation_section_tool("ISO 13485:2016", "Quality management")
            if reg_section:
                reg_data = json.loads(reg_section)
                print("      ✅ ISO 13485引用验证通过")
                verification_results.append("ISO_13485_verified")
        
        # 验证市场数据
        if "market_data" in expert_result["tools_used"]:
            print("   2. 验证市场数据...")
            current_data = market_data_tool("market_size", "2023")
            print("      ✅ 市场数据验证通过")
            verification_results.append("market_data_verified")
        
        # 验证技术信息
        if "technology_trends" in expert_result["tools_used"]:
            print("   3. 验证技术趋势...")
            tech_data = market_data_tool("technology_trends")
            print("      ✅ 技术趋势数据验证通过")
            verification_results.append("tech_trends_verified")
        
        # 生成评价结果
        evaluation_result = self._generate_mock_evaluation(expert_result, verification_results)
        
        print(f"\n📈 评价结果:")
        print(f"   - 总体评分: {evaluation_result['overall_score']}/10")
        print(f"   - 科学严谨性: {evaluation_result['scientific_rigor']}/10")
        print(f"   - 证据充分性: {evaluation_result['evidence_sufficiency']}/10")
        print(f"   - 客观中立性: {evaluation_result['objectivity']}/10")
        print(f"   - 验证通过项: {len(verification_results)} 项")
        
        return {
            "system_prompt": system_prompt,
            "user_prompt": user_prompt,
            "evaluation": evaluation_result,
            "verification_results": verification_results
        }
    
    def complete_consultation(self, question: str, question_type: str, context: str = ""):
        """完整的咨询流程"""
        
        print(f"\n🚀 完整IVD咨询流程")
        print("="*80)
        print(f"咨询问题: {question}")
        print(f"问题类型: {question_type}")
        print(f"背景信息: {context}")
        
        # 第一步：专家分析
        expert_result = self.analyze_with_expert(question, question_type, context)
        
        # 第二步：评价专家评估
        evaluation_result = self.evaluate_with_evaluator(
            expert_result, question, question_type, context
        )
        
        # 第三步：生成最终建议
        print(f"\n🎯 最终咨询结果")
        print("="*60)
        
        final_recommendation = self._generate_final_recommendation(
            expert_result, evaluation_result, question_type
        )
        
        print(final_recommendation)
        
        return {
            "expert_analysis": expert_result,
            "quality_evaluation": evaluation_result,
            "final_recommendation": final_recommendation
        }
    
    def _generate_mock_expert_analysis(self, question_type: str, question: str, tools_used: list) -> str:
        """生成模拟的专家分析"""
        
        analysis_templates = {
            "技术分析": f"""
## 技术分析报告

### 执行摘要
基于最新的技术发展和市场数据，针对"{question}"进行了全面的技术分析。

### 技术原理分析
根据ISO 13485:2016质量管理体系要求，技术开发需要遵循严格的设计控制流程。

### 性能评估
- 分析敏感性: >95%
- 特异性: >98%
- 检测限: 符合CLSI EP17-A2标准要求

### 法规要求
- FDA 21 CFR Part 820设计控制要求
- EU IVDR 2017/746性能评估要求
- ISO 14971风险管理标准

### 市场前景
全球IVD市场2023年规模102.1亿美元，预期年增长率6.1%。
分子诊断细分市场占25%份额，年增长率8.5%。

### 建议
建议采用分阶段开发策略，优先满足FDA和CE认证要求。
            """,
            
            "法规合规性": f"""
## 法规合规性分析

### 执行摘要
针对"{question}"的法规要求进行了详细分析，涵盖主要市场的合规路径。

### 美国FDA要求
- 产品分类: II类医疗器械
- 注册路径: 510(k) Premarket Notification
- 设计控制: 21 CFR Part 820 Subpart C要求
- 预期审评时间: 90-180天

### 欧盟IVDR要求
- 风险分类: C类器械
- 公告机构参与: 全面技术文档审查
- 性能评估: 分析性能+临床性能研究
- UDI要求: 2027年5月26日前实施

### ISO标准要求
- ISO 13485:2016质量管理体系
- ISO 14971:2019风险管理
- ISO 15189:2022实验室质量要求

### 合规建议
建议尽早启动法规咨询，预计总体注册成本50-100万美元。
            """,
            
            "市场和商业分析": f"""
## 市场和商业分析

### 市场概况
2023年全球IVD市场规模102.1亿美元，预期2030年达154.3亿美元。

### 细分市场分析
- 分子诊断: 25%份额，8.5%增长率
- 免疫检测: 35%份额，5.2%增长率
- 生化诊断: 20%份额，4.8%增长率

### 区域分析
- 北美市场: 40%份额，成熟市场
- 欧洲市场: 30%份额，法规驱动
- 亚太市场: 25%份额，高速增长

### 竞争格局
主要参与者包括Roche、Abbott、Siemens Healthineers等。

### 商业机会
建议重点关注POCT和分子诊断领域的技术创新。
            """
        }
        
        return analysis_templates.get(question_type, "通用技术分析内容...")
    
    def _generate_mock_evaluation(self, expert_result: dict, verification_results: list) -> dict:
        """生成模拟的评价结果"""
        
        base_score = 7.0
        
        # 根据工具使用情况调整分数
        if len(expert_result["tools_used"]) >= 2:
            base_score += 1.0
        
        # 根据验证结果调整分数
        if len(verification_results) >= 2:
            base_score += 0.5
        
        return {
            "overall_score": min(base_score, 10.0),
            "scientific_rigor": min(base_score + 0.5, 10.0),
            "evidence_sufficiency": min(base_score, 10.0),
            "objectivity": min(base_score - 0.5, 10.0),
            "completeness": min(base_score + 0.2, 10.0),
            "practical_value": min(base_score + 0.3, 10.0)
        }
    
    def _generate_final_recommendation(self, expert_result: dict, evaluation_result: dict, 
                                     question_type: str) -> str:
        """生成最终建议"""
        
        score = evaluation_result["evaluation"]["overall_score"]
        
        if score >= 8.5:
            quality_level = "优秀"
            recommendation = "分析质量优秀，建议直接采用相关建议。"
        elif score >= 7.0:
            quality_level = "良好"
            recommendation = "分析质量良好，建议结合实际情况采用。"
        elif score >= 5.5:
            quality_level = "合格"
            recommendation = "分析质量合格，建议补充更多数据支撑。"
        else:
            quality_level = "需改进"
            recommendation = "分析质量需要改进，建议重新分析。"
        
        return f"""
### 最终咨询建议

**质量评级**: {quality_level} ({score:.1f}/10)

**专业分析**: 
{expert_result['analysis'][:200]}...

**质量评估**: 
- 使用了 {len(expert_result['tools_used'])} 个专业工具
- 通过了 {len(evaluation_result['verification_results'])} 项验证
- 科学严谨性: {evaluation_result['evaluation']['scientific_rigor']:.1f}/10

**最终建议**: {recommendation}

**下一步行动**:
1. 根据评价结果优化分析内容
2. 补充必要的数据和证据支撑
3. 考虑多方专家意见进行验证
4. 制定具体的实施计划
        """


def main():
    """主演示函数"""
    
    print("🧬 IVD专家系统最终演示")
    print("="*80)
    print("这是一个完整的IVD专业咨询系统，包含:")
    print("✅ 法规知识库 (ISO 13485, FDA 21 CFR Part 820, EU IVDR)")
    print("✅ 搜索工具函数 (法规搜索、市场数据、文献检索)")
    print("✅ IVD专家Agent (专业分析和建议)")
    print("✅ 评价专家Agent (质量评估和验证)")
    print("✅ 完整的质量控制闭环")
    print("="*80)
    
    # 初始化系统
    system = IVDExpertSystem()
    
    # 演示案例
    demo_cases = [
        {
            "question": "请分析qPCR和dPCR技术在液体活检中的应用优势，包括技术原理、性能对比和法规要求",
            "question_type": "技术分析",
            "context": "我们公司正在开发液体活检产品，目标市场为欧美，需要选择合适的核酸检测技术"
        },
        {
            "question": "我们的POCT产品准备申请FDA 510(k)，请分析注册流程、技术要求和可能的风险",
            "question_type": "法规合规性", 
            "context": "产品是基于免疫层析技术的心肌标志物检测试剂盒，与Abbott i-STAT产品对比"
        }
    ]
    
    for i, case in enumerate(demo_cases, 1):
        print(f"\n" + "="*80)
        print(f"演示案例 {i}")
        print("="*80)
        
        result = system.complete_consultation(
            case["question"],
            case["question_type"], 
            case["context"]
        )
        
        print(f"\n✅ 案例 {i} 演示完成")
    
    print(f"\n" + "="*80)
    print("🎉 IVD专家系统演示完成！")
    print("="*80)
    print("系统特点:")
    print("🔬 专业深度: 15年+行业经验，权威数据支撑")
    print("📊 客观评价: 20年+学术背景，批判性思维")
    print("🔧 工具集成: 法规搜索、市场数据、文献检索")
    print("📚 知识丰富: 完整的法规标准和技术知识库")
    print("🎯 质量控制: 分析→评价→改进的完整闭环")
    print("="*80)


if __name__ == "__main__":
    main()
