#!/usr/bin/env python3
"""
测试IVD专家和评价专家Agent的协同工作

这个脚本演示如何使用IVD专家agent生成分析，
然后使用评价专家agent对分析进行质量评价。
"""

import sys
from pathlib import Path

# 添加prompt_manager到Python路径
sys.path.append(str(Path(__file__).parent / "prompt_manager"))

from prompt_manager import PromptManager


class IVDAgentsCollaboration:
    """IVD专家和评价专家协同工作类"""
    
    def __init__(self, prompt_store_path="./ivd_prompt_store"):
        """初始化两个agent"""
        self.pm = PromptManager(prompt_store_path)
        self.expert_prompts = self.pm.get_prompts("ivd_expert")
        self.evaluator_prompts = self.pm.get_prompts("ivd_evaluator")
        
    def generate_expert_analysis_prompt(self, question_type, user_question, context_info=""):
        """生成专家分析提示词"""
        system_prompt = self.expert_prompts['system'].format()
        user_prompt = self.expert_prompts['user'].format(
            question_type=question_type,
            user_question=user_question,
            context_info=context_info
        )
        return system_prompt, user_prompt
    
    def generate_evaluation_prompt(self, original_question_type, original_question, 
                                 original_context, analysis_content):
        """生成评价提示词"""
        system_prompt = self.evaluator_prompts['system'].format()
        user_prompt = self.evaluator_prompts['user'].format(
            original_question_type=original_question_type,
            original_question=original_question,
            original_context=original_context,
            analysis_content=analysis_content
        )
        return system_prompt, user_prompt
    
    def demonstrate_collaboration(self, question_type, user_question, context_info="", 
                                mock_analysis=""):
        """演示两个agent的协同工作"""
        
        print("="*80)
        print("IVD专家和评价专家协同工作演示")
        print("="*80)
        
        # 第一步：生成专家分析提示词
        print("\n【第一步：IVD专家分析】")
        print("-"*50)
        expert_system, expert_user = self.generate_expert_analysis_prompt(
            question_type, user_question, context_info
        )
        
        print("专家分析提示词已生成")
        print(f"系统提示词长度: {len(expert_system)} 字符")
        print(f"用户提示词长度: {len(expert_user)} 字符")
        print("\n用户提示词预览:")
        print(expert_user[:500] + "..." if len(expert_user) > 500 else expert_user)
        
        # 第二步：模拟分析结果（实际使用中这里是AI模型的输出）
        if not mock_analysis:
            mock_analysis = self._generate_mock_analysis(question_type)
        
        print(f"\n【模拟的专家分析结果】")
        print("-"*50)
        print(mock_analysis[:800] + "..." if len(mock_analysis) > 800 else mock_analysis)
        
        # 第三步：生成评价提示词
        print(f"\n【第二步：评价专家评价】")
        print("-"*50)
        eval_system, eval_user = self.generate_evaluation_prompt(
            question_type, user_question, context_info, mock_analysis
        )
        
        print("评价提示词已生成")
        print(f"系统提示词长度: {len(eval_system)} 字符")
        print(f"用户提示词长度: {len(eval_user)} 字符")
        
        return {
            'expert_system': expert_system,
            'expert_user': expert_user,
            'analysis_content': mock_analysis,
            'evaluator_system': eval_system,
            'evaluator_user': eval_user
        }
    
    def _generate_mock_analysis(self, question_type):
        """生成模拟的分析内容用于演示"""
        
        mock_analyses = {
            "技术分析": """
## 执行摘要
qPCR和dPCR技术各有优势，qPCR适合高通量检测，dPCR适合绝对定量和稀有突变检测。对于液体活检应用，建议根据具体检测需求选择合适的技术平台。

## 详细技术分析

### qPCR技术特点
- **检测原理**：基于PCR扩增过程中荧光信号的实时监测
- **定量方式**：相对定量，需要标准曲线
- **检测限**：通常为10-100 copies/mL
- **动态范围**：6-8个数量级
- **通量**：高，可同时检测96-384个样本

### dPCR技术特点  
- **检测原理**：将样本分割成数千个微反应室进行PCR
- **定量方式**：绝对定量，基于泊松分布统计
- **检测限**：可达1 copy/μL
- **精密度**：CV < 10%
- **通量**：相对较低

### 液体活检应用建议
基于0.1% VAF的检测要求和每天100-200样本的通量需求，建议：
1. 主要采用qPCR平台满足高通量需求
2. 关键样本使用dPCR进行验证
3. 考虑成本效益，qPCR成本约$5-10/样本，dPCR约$15-25/样本

### 参考文献
1. Huggett JF, et al. The digital MIQE guidelines. Clin Chem. 2013;59(6):892-902.
2. Whale AS, et al. Comparison of microfluidic digital PCR and conventional quantitative PCR. Nucleic Acids Res. 2012;40(11):e82.
            """,
            
            "法规合规性": """
## 执行摘要
POCT心肌标志物检测产品申请FDA 510(k)需要进行充分的分析性能验证和临床性能评估，预计注册周期6-12个月，成本约50-100万美元。

## 法规分析

### 510(k)申请要求
- **产品分类**：II类医疗器械，产品代码JJE
- **谓词器械**：Abbott i-STAT cTnI为合适的谓词器械
- **实质等同性**：需证明在安全性和有效性方面实质等同

### 技术要求
- **分析性能**：精密度、准确度、线性、检出限等
- **临床性能**：敏感性≥95%，特异性≥95%
- **干扰研究**：血红蛋白、胆红素、甘油三酯等内源性干扰
- **稳定性研究**：实时稳定性和加速稳定性

### 临床试验设计
- **样本量**：至少200例急性心肌梗死患者
- **对照方法**：与FDA批准的cTnI检测方法比较
- **统计分析**：Deming回归分析，相关系数r≥0.95

### 风险评估
- **技术风险**：侧向流技术的精密度挑战
- **法规风险**：FDA对POCT产品要求日趋严格
- **时间风险**：可能需要额外的临床数据

### 建议
1. 尽早与FDA进行Pre-Submission会议
2. 参考最新的CLSI EP指南进行验证
3. 考虑聘请专业的法规咨询公司
            """,
            
            "市场和商业分析": """
## 执行摘要
全球NGS市场预计2030年达到$47.8亿美元，临床应用是主要增长驱动力。建议重点关注肿瘤诊断和遗传病检测领域的投资机会。

## 市场分析

### 市场规模和增长
- **2023年市场规模**：$15.6亿美元
- **预期CAGR (2023-2030)**：15.1%
- **2030年预测规模**：$47.8亿美元

### 细分市场分析
- **肿瘤诊断**：占比45%，年增长率18%
- **遗传病检测**：占比25%，年增长率12%
- **感染病诊断**：占比20%，年增长率10%
- **其他应用**：占比10%

### 竞争格局
1. **Illumina**：市场份额65%，技术领先
2. **Thermo Fisher**：市场份额15%，产品线完整
3. **Roche**：市场份额8%，临床应用强
4. **新兴企业**：Oxford Nanopore、PacBio等

### 投资机会
- **技术创新**：长读长测序、单细胞测序
- **应用拓展**：液体活检、微生物检测
- **成本降低**：测序成本持续下降
- **监管环境**：FDA批准更多NGS产品

### 风险因素
- **技术替代**：新兴技术可能颠覆现有格局
- **价格竞争**：激烈的价格竞争压缩利润
- **监管变化**：监管政策变化影响市场准入

### 投资建议
1. 重点关注临床应用领域的公司
2. 评估技术壁垒和专利保护
3. 考虑监管风险和市场准入能力
4. 关注成本控制和规模化能力
            """,
            
            "临床应用价值": """
## 执行摘要
液体活检技术在肺癌早期诊断中显示出良好前景，但仍需要更多高质量临床证据。建议分阶段实施，先用于高危人群筛查。

## 临床价值分析

### 技术可行性
- **检测原理**：循环肿瘤DNA (ctDNA)检测
- **敏感性**：早期肺癌检测敏感性60-80%
- **特异性**：>95%，假阳性率低
- **检测时间**：7-14天符合临床需求

### 诊断性能评估
- **与影像学比较**：CT筛查敏感性85-90%
- **与组织活检比较**：一致性约80-85%
- **早期检出能力**：可检出I-II期肺癌
- **监测价值**：可用于治疗监测和复发预测

### 成本效益分析
- **检测成本**：3000-5000元/例
- **成本效益比**：约$50,000/QALY
- **预算影响**：年检测5000例，总成本1500-2500万元
- **经济效益**：早期发现可节省后期治疗成本

### 实施挑战
- **技术标准化**：缺乏统一的技术标准
- **质量控制**：需要严格的质量控制体系
- **人员培训**：需要专业的技术人员
- **设备投入**：需要购置专业设备

### 临床证据
- **NELSON研究**：CT筛查降低肺癌死亡率24%
- **CIRCULATE研究**：液体活检用于肺癌筛查的前瞻性研究
- **证据等级**：目前主要为2B级证据，需要更多RCT

### 实施建议
1. 先在高危人群中开展试点
2. 建立标准化的检测流程
3. 加强与影像学检查的结合
4. 开展卫生经济学评估
5. 制定质量控制标准
            """
        }
        
        return mock_analyses.get(question_type, "这是一个示例分析内容...")


def test_collaboration_scenarios():
    """测试不同场景下的协同工作"""
    
    collaboration = IVDAgentsCollaboration()
    
    scenarios = [
        {
            "name": "技术分析场景",
            "question_type": "技术分析",
            "user_question": "请分析qPCR和dPCR技术在液体活检中的应用优势和局限性",
            "context_info": "我们公司正在开发液体活检产品，需要选择合适的核酸检测技术平台。目标检测限为0.1% VAF，样本通量为每天100-200个样本。"
        },
        {
            "name": "法规咨询场景", 
            "question_type": "法规合规性",
            "user_question": "POCT心肌标志物检测产品申请FDA 510(k)的流程和要求",
            "context_info": "产品是基于侧向流免疫技术的cTnI检测试剂盒，预期用于急诊科和ICU，与Abbott i-STAT cTnI产品进行对比。"
        }
    ]
    
    print("开始测试IVD专家和评价专家的协同工作...\n")
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{'='*60}")
        print(f"场景 {i}: {scenario['name']}")
        print(f"{'='*60}")
        
        result = collaboration.demonstrate_collaboration(
            scenario['question_type'],
            scenario['user_question'], 
            scenario['context_info']
        )
        
        print(f"\n✅ {scenario['name']} 协同工作演示完成")
        print(f"   - 专家分析提示词: {len(result['expert_user'])} 字符")
        print(f"   - 模拟分析内容: {len(result['analysis_content'])} 字符")
        print(f"   - 评价提示词: {len(result['evaluator_user'])} 字符")


def demonstrate_quality_control_workflow():
    """演示质量控制工作流程"""
    
    print("\n" + "="*80)
    print("IVD分析质量控制工作流程演示")
    print("="*80)
    
    collaboration = IVDAgentsCollaboration()
    
    # 模拟一个有问题的分析
    problematic_analysis = """
## 技术分析
CRISPR诊断技术是革命性的突破，完全替代了传统PCR技术。
该技术具有100%的敏感性和特异性，检测时间仅需5分钟。
所有病原体都可以用CRISPR技术检测，没有任何局限性。
成本极低，每次检测仅需1美元。

## 市场前景
CRISPR诊断市场将在2025年达到1000亿美元。
所有实验室都将采用CRISPR技术。
传统检测技术将完全消失。

## 结论
CRISPR诊断技术是完美的解决方案，没有任何缺点。
    """
    
    print("\n【问题分析示例】")
    print("-"*50)
    print("以下是一个存在明显问题的IVD技术分析:")
    print(problematic_analysis)
    
    print("\n【评价专家评价】")
    print("-"*50)
    
    eval_system, eval_user = collaboration.generate_evaluation_prompt(
        "技术分析",
        "请分析CRISPR诊断技术的优势和应用前景",
        "用于病原体检测产品开发",
        problematic_analysis
    )
    
    print("评价提示词已生成，将识别以下问题:")
    print("1. 过度技术乐观 - 声称'革命性突破'、'完全替代'")
    print("2. 性能数据不实 - 声称'100%敏感性特异性'")
    print("3. 成本估算不现实 - '每次检测1美元'")
    print("4. 市场预测过于乐观 - '1000亿美元市场'")
    print("5. 缺乏客观性 - '完美解决方案'、'没有缺点'")
    
    print(f"\n评价提示词长度: {len(eval_user)} 字符")
    print("评价专家将提供详细的问题诊断和改进建议。")


def main():
    """主函数"""
    
    print("🔬 IVD专家和评价专家协同工作测试")
    print("="*60)
    print("这个测试展示两个agent如何协同工作:")
    print("1. IVD专家生成专业分析")
    print("2. 评价专家评估分析质量")
    print("3. 形成完整的质量控制闭环")
    print("="*60)
    
    # 测试协同工作场景
    test_collaboration_scenarios()
    
    # 演示质量控制工作流程
    demonstrate_quality_control_workflow()
    
    print("\n" + "="*80)
    print("协同工作测试完成！")
    print("\n💡 使用建议:")
    print("1. 先使用IVD专家agent生成分析")
    print("2. 将分析结果输入评价专家agent进行质量评估")
    print("3. 根据评价结果改进分析质量")
    print("4. 重复此过程直到达到满意的质量水平")
    print("\n🔧 集成建议:")
    print("- 可以构建自动化的质量控制流水线")
    print("- 支持批量分析和评价")
    print("- 可以设置质量阈值自动筛选")
    print("="*80)


if __name__ == "__main__":
    main()
