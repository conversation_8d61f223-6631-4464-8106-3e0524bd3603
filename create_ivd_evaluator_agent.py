#!/usr/bin/env python3
"""
创建IVD分析评价专家Agent

这个脚本使用PromptManager创建一个专业的IVD分析评价专家agent，
该agent专门用于客观、中立地评价IVD相关分析报告的质量。
"""

import os
import sys
from pathlib import Path

# 添加prompt_manager到Python路径
sys.path.append(str(Path(__file__).parent / "prompt_manager"))

from prompt_manager import PromptManager, PromptConfigurationError, AgentAlreadyExistsError


def load_file_content(file_path: str) -> str:
    """加载文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        sys.exit(1)
    except Exception as e:
        print(f"错误：读取文件 {file_path} 失败: {e}")
        sys.exit(1)


def create_ivd_evaluator_agent():
    """创建IVD评价专家agent"""
    
    print("=== 创建IVD分析评价专家Agent ===\n")
    
    try:
        # 初始化PromptManager
        print("1. 初始化PromptManager...")
        pm = PromptManager("./ivd_prompt_store")
        print(f"   已初始化，当前有 {len(pm.list_agents())} 个agent\n")
        
        # 加载系统提示词
        print("2. 加载评价专家系统提示词...")
        system_message = load_file_content("ivd_evaluator_system_prompt.md")
        print(f"   系统提示词已加载，长度: {len(system_message)} 字符\n")
        
        # 加载用户消息模板
        print("3. 加载评价专家用户消息模板...")
        user_message = load_file_content("ivd_evaluator_user_prompt.md")
        print(f"   用户消息模板已加载，长度: {len(user_message)} 字符\n")
        
        # 加载评价标准知识库
        print("4. 加载评价标准知识库...")
        evaluation_standards = load_file_content("ivd_evaluation_standards.md")
        print(f"   评价标准知识库已加载，长度: {len(evaluation_standards)} 字符\n")
        
        # 加载常见问题识别知识库
        print("5. 加载常见问题识别知识库...")
        common_issues = load_file_content("ivd_common_issues.md")
        print(f"   常见问题识别知识库已加载，长度: {len(common_issues)} 字符\n")
        
        # 创建评价方法论知识库
        print("6. 创建评价方法论知识库...")
        evaluation_methodology = """# IVD分析评价方法论知识库

## 循证评价方法

### GRADE评价体系
- **证据质量评估**：
  - 高质量：进一步研究不太可能改变效应估计的可信度
  - 中等质量：进一步研究可能对效应估计的可信度有重要影响
  - 低质量：进一步研究很可能对效应估计的可信度有重要影响
  - 极低质量：任何效应估计都非常不确定

### 系统评价方法
- **PRISMA指南**：系统评价和Meta分析报告规范
- **STARD指南**：诊断准确性研究报告标准
- **CONSORT指南**：随机对照试验报告标准
- **STROBE指南**：观察性研究报告标准

## 批判性评价框架

### 内部效度评估
- **选择偏倚**：研究对象选择是否代表目标人群
- **信息偏倚**：测量误差和分类误差的影响
- **混杂偏倚**：是否控制了重要的混杂因素
- **随访偏倚**：失访是否与结局相关

### 外部效度评估
- **人群代表性**：研究人群是否代表目标人群
- **环境适用性**：研究环境是否适用于实际应用
- **时间相关性**：研究结果是否适用于当前时期
- **地区适用性**：研究结果的地区适用性

## 定量评价方法

### 诊断准确性评价
- **敏感性分析**：改变关键参数对结果的影响
- **亚组分析**：不同亚组的诊断性能差异
- **ROC分析**：受试者工作特征曲线分析
- **似然比计算**：阳性和阴性似然比的计算和解释

### 经济评价方法
- **成本效果分析**：成本与健康结局的比较
- **成本效用分析**：成本与质量调整生命年的比较
- **预算影响分析**：对医疗预算的影响评估
- **敏感性分析**：关键参数变化对结果的影响

## 质量评价工具

### 诊断研究质量评价
- **QUADAS-2工具**：诊断准确性研究质量评价
- **STARD清单**：诊断研究报告质量检查
- **HSROC模型**：分层汇总ROC模型

### 经济评价质量评价
- **CHEERS清单**：健康经济评价报告标准
- **Drummond清单**：经济评价质量评价
- **ISPOR指南**：药物经济学研究指南

## 专家共识方法

### Delphi方法
- **专家选择**：选择具有代表性的专家组
- **问卷设计**：结构化问卷设计原则
- **共识标准**：达成共识的标准和方法
- **结果解释**：共识结果的解释和应用

### 名义组技术
- **结构化讨论**：有序的专家讨论过程
- **匿名投票**：避免权威影响的投票机制
- **优先级排序**：问题和解决方案的优先级排序
- **共识形成**：通过迭代达成共识

## 偏倚识别与控制

### 认知偏倚识别
- **确认偏倚**：寻找支持预设观点的信息
- **可得性偏倚**：过度依赖容易获得的信息
- **代表性偏倚**：基于有限样本的过度概括
- **锚定偏倚**：过度依赖初始信息

### 偏倚控制方法
- **盲法评价**：评价者对研究假设保持盲态
- **多重评价**：多个评价者独立评价
- **标准化流程**：使用标准化的评价流程
- **质量控制**：定期的质量控制和校准

## 不确定性评估

### 参数不确定性
- **置信区间**：参数估计的不确定性范围
- **概率分布**：参数的概率分布假设
- **蒙特卡洛模拟**：参数不确定性的传播分析
- **敏感性分析**：关键参数对结果的影响

### 模型不确定性
- **结构不确定性**：模型结构选择的不确定性
- **假设不确定性**：模型假设的不确定性
- **外推不确定性**：模型外推的不确定性
- **验证分析**：模型预测能力的验证

## 评价报告标准

### 报告结构
- **执行摘要**：简明扼要的评价结论
- **方法描述**：详细的评价方法和标准
- **结果呈现**：系统化的评价结果
- **讨论分析**：深入的讨论和分析
- **建议提出**：具体的改进建议

### 质量保证
- **同行评议**：专家同行的评议
- **透明度**：评价过程和标准的透明
- **可重现性**：评价结果的可重现性
- **持续改进**：基于反馈的持续改进"""
        
        # 创建评价案例知识库
        print("7. 创建评价案例知识库...")
        evaluation_cases = """# IVD分析评价案例知识库

## 优秀分析案例特征

### 技术分析优秀案例
- **全面性**：覆盖技术原理、性能参数、应用场景、局限性
- **准确性**：技术描述准确，数据可靠，引用权威
- **客观性**：平衡讨论优势和劣势，承认不确定性
- **深度性**：提供深入的技术洞察和专业判断

### 法规分析优秀案例
- **时效性**：使用最新的法规信息和指导原则
- **完整性**：覆盖完整的注册流程和要求
- **准确性**：对法规要求的理解准确无误
- **实用性**：提供具体可操作的合规建议

### 市场分析优秀案例
- **数据权威**：使用权威机构的市场数据
- **方法科学**：采用科学的市场分析方法
- **预测合理**：基于充分证据的合理预测
- **风险平衡**：平衡讨论机会和风险

### 临床分析优秀案例
- **证据充分**：基于高质量的临床证据
- **需求明确**：明确定义临床需求和目标人群
- **价值量化**：量化临床价值和经济效益
- **实施可行**：考虑实际实施的可行性

## 常见问题案例

### 技术分析问题案例
**案例1：过度技术乐观**
- 问题：声称新技术"革命性突破"，"完全替代现有技术"
- 识别：缺乏客观比较，忽视技术局限性
- 改进：提供客观的技术对比，承认局限性和挑战

**案例2：性能数据不实**
- 问题：使用理想条件下的实验室数据代表实际性能
- 识别：缺乏真实世界验证数据
- 改进：提供多种条件下的性能验证数据

### 法规分析问题案例
**案例1：法规信息过时**
- 问题：引用已废止的FDA指导原则
- 识别：发布时间过早，未标注版本信息
- 改进：使用最新版本的法规文件，标注更新时间

**案例2：流程过度简化**
- 问题：将复杂的510(k)流程简化为"提交文件即可"
- 识别：关键步骤缺失，时间估算不现实
- 改进：详细描述各个步骤和潜在风险

### 市场分析问题案例
**案例1：数据来源不可靠**
- 问题：引用无名公司的市场报告
- 识别：数据来源不权威，方法论不透明
- 改进：使用权威机构数据，说明数据来源和方法

**案例2：预测过于乐观**
- 问题：预测成熟市场年增长率30%
- 识别：增长率明显超出历史水平
- 改进：基于历史数据和驱动因素进行合理预测

### 临床分析问题案例
**案例1：临床价值夸大**
- 问题：声称检测技术"拯救生命"，"改变医疗"
- 识别：缺乏量化的临床获益证据
- 改进：提供具体的临床获益数据和证据

**案例2：成本效益简化**
- 问题：只计算检测成本，忽视其他相关成本
- 识别：成本计算不完整，效益估算过高
- 改进：全面计算成本，合理估算效益

## 评价标准应用案例

### 科学严谨性评价案例
**高分案例特征**：
- 统计方法正确，样本量充足
- 实验设计合理，控制偏倚
- 数据质量高，结果可重现
- 不确定性得到适当讨论

**低分案例特征**：
- 统计方法错误或不适当
- 实验设计有明显缺陷
- 数据质量差，结果不可信
- 忽视不确定性和局限性

### 证据充分性评价案例
**高分案例特征**：
- 引用高质量的系统评价和RCT
- 证据与结论高度相关
- 平衡引用支持和反对证据
- 证据来源权威可靠

**低分案例特征**：
- 主要依赖低质量证据
- 证据与结论相关性差
- 选择性引用支持证据
- 证据来源不可靠

### 客观中立性评价案例
**高分案例特征**：
- 语言客观中性
- 平衡讨论优缺点
- 承认不确定性和局限性
- 无明显商业偏见

**低分案例特征**：
- 语言带有明显倾向性
- 过度强调优势，忽视劣势
- 过于绝对的结论
- 存在明显商业偏见

## 改进建议案例

### 结构性改进案例
**问题**：分析逻辑混乱，结构不清晰
**建议**：
1. 重新组织内容结构
2. 明确各部分的逻辑关系
3. 使用标准的分析框架
4. 提供清晰的结论总结

### 内容改进案例
**问题**：关键信息缺失，分析不够深入
**建议**：
1. 补充缺失的关键信息
2. 增加分析的深度和洞察力
3. 提供更多的数据支撑
4. 加强与实际应用的联系

### 方法改进案例
**问题**：分析方法不够科学严谨
**建议**：
1. 采用更科学的分析方法
2. 加强统计分析的严谨性
3. 提高证据评估的质量
4. 增强偏倚控制措施

## 评价质量控制案例

### 评价者间一致性
- **高一致性案例**：多个评价者评分差异<1分
- **低一致性案例**：多个评价者评分差异>3分
- **改进措施**：加强评价标准培训，定期校准

### 评价可重现性
- **高重现性案例**：重复评价结果高度一致
- **低重现性案例**：重复评价结果差异较大
- **改进措施**：标准化评价流程，详细记录评价过程

### 评价有效性验证
- **有效性验证**：评价结果与专家判断一致
- **有效性问题**：评价结果与专家判断存在分歧
- **改进措施**：调整评价标准，完善评价方法"""
        
        # 注册IVD评价专家agent
        print("8. 注册IVD评价专家agent...")
        agent_name = "ivd_evaluator"
        
        # 检查agent是否已存在
        if agent_name in pm.list_agents():
            print(f"   警告：Agent '{agent_name}' 已存在，正在删除...")
            pm.delete_agent(agent_name)
            print(f"   已删除现有agent '{agent_name}'")
        
        pm.register_new_agent(
            agent_name=agent_name,
            system_message=system_message,
            user_message=user_message,
            evaluation_standards=evaluation_standards,
            common_issues=common_issues,
            evaluation_methodology=evaluation_methodology,
            evaluation_cases=evaluation_cases
        )
        print(f"   Agent '{agent_name}' 注册成功！\n")
        
        # 验证agent创建
        print("9. 验证agent创建...")
        agents = pm.list_agents()
        print(f"   当前agents: {agents}")
        
        if agent_name in agents:
            info = pm.get_agent_info(agent_name)
            print(f"   Agent信息: {info}\n")
            
            # 获取prompts信息
            prompts = pm.get_prompts(agent_name)
            print(f"   系统模板变量: {prompts['system'].input_variables}")
            print(f"   用户模板变量: {prompts['user'].input_variables}\n")
            
            print("=== IVD评价专家Agent创建成功！ ===")
            print(f"Agent名称: {agent_name}")
            print(f"存储路径: {pm.store_path / agent_name}")
            print(f"知识模块: {len(info['variables'])} 个")
            print("\n可用的知识模块:")
            for var in info['variables']:
                print(f"  - {var}")
                
        else:
            print("   错误：Agent创建失败")
            
    except AgentAlreadyExistsError as e:
        print(f"Agent已存在错误: {e}")
        if hasattr(e, 'context'):
            print(f"上下文: {e.context}")
    
    except PromptConfigurationError as e:
        print(f"配置错误: {e}")
        if hasattr(e, 'context'):
            print(f"上下文: {e.context}")
    
    except Exception as e:
        print(f"意外错误: {e}")
        import traceback
        traceback.print_exc()


def test_ivd_evaluator_agent():
    """测试IVD评价专家agent"""
    
    print("\n=== 测试IVD评价专家Agent ===\n")
    
    try:
        # 初始化PromptManager
        pm = PromptManager("./ivd_prompt_store")
        
        # 获取agent prompts
        prompts = pm.get_prompts("ivd_evaluator")
        
        # 测试系统提示词
        print("1. 测试系统提示词...")
        system_prompt = prompts['system'].format()
        print(f"   系统提示词长度: {len(system_prompt)} 字符")
        print(f"   前200字符预览: {system_prompt[:200]}...\n")
        
        # 测试用户提示词
        print("2. 测试用户提示词...")
        user_prompt = prompts['user'].format(
            original_question_type="技术分析",
            original_question="请分析qPCR和dPCR技术的优缺点",
            original_context="用于液体活检产品开发",
            analysis_content="这是一个示例分析内容，用于测试评价功能..."
        )
        print(f"   用户提示词长度: {len(user_prompt)} 字符")
        print(f"   格式化成功！\n")
        
        print("=== 测试完成，评价专家Agent可正常使用！ ===")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 创建IVD评价专家agent
    create_ivd_evaluator_agent()
    
    # 测试agent功能
    test_ivd_evaluator_agent()
    
    print("\n" + "="*50)
    print("IVD评价专家Agent已成功创建并测试完成！")
    print("您现在可以使用这个专业的评价专家来评估IVD分析质量。")
    print("="*50)
