#!/usr/bin/env python3
"""
创建IVD领域专家Agent

这个脚本使用PromptManager创建一个专业的体外诊断(IVD)领域专家agent，
该agent具有深度的专业知识、数据思维和权威的分析能力。
"""

import os
import sys
from pathlib import Path

# 添加prompt_manager到Python路径
sys.path.append(str(Path(__file__).parent / "prompt_manager"))

from prompt_manager import PromptManager, PromptConfigurationError, AgentAlreadyExistsError


def load_file_content(file_path: str) -> str:
    """加载文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        sys.exit(1)
    except Exception as e:
        print(f"错误：读取文件 {file_path} 失败: {e}")
        sys.exit(1)


def create_ivd_expert_agent():
    """创建IVD专家agent"""
    
    print("=== 创建IVD领域专家Agent ===\n")
    
    try:
        # 初始化PromptManager
        print("1. 初始化PromptManager...")
        pm = PromptManager("./ivd_prompt_store")
        print(f"   已初始化，当前有 {len(pm.list_agents())} 个agent\n")
        
        # 加载系统提示词
        print("2. 加载系统提示词...")
        system_message = load_file_content("ivd_expert_system_prompt.md")
        print(f"   系统提示词已加载，长度: {len(system_message)} 字符\n")
        
        # 加载用户消息模板
        print("3. 加载用户消息模板...")
        user_message = load_file_content("ivd_expert_user_prompt.md")
        print(f"   用户消息模板已加载，长度: {len(user_message)} 字符\n")
        
        # 加载技术知识库
        print("4. 加载技术知识库...")
        technical_knowledge = load_file_content("ivd_technical_knowledge.md")
        print(f"   技术知识库已加载，长度: {len(technical_knowledge)} 字符\n")
        
        # 加载法规知识库
        print("5. 加载法规知识库...")
        regulatory_knowledge = load_file_content("ivd_regulatory_knowledge.md")
        print(f"   法规知识库已加载，长度: {len(regulatory_knowledge)} 字符\n")
        
        # 创建市场分析知识库
        print("6. 创建市场分析知识库...")
        market_knowledge = """# IVD市场分析知识库

## 全球市场概况

### 市场规模和增长
- **2023年全球IVD市场规模**：约$102.1亿美元
- **预期CAGR (2023-2030)**：6.1%
- **2030年预测市场规模**：约$154.3亿美元

### 区域分布
- **北美市场**：占比约40%，主要驱动因素为技术创新和高医疗支出
- **欧洲市场**：占比约30%，IVDR法规推动市场规范化
- **亚太市场**：占比约25%，增长最快，CAGR约8.2%
- **其他地区**：占比约5%

## 主要厂商竞争格局

### 头部企业市场份额
1. **Roche Diagnostics**：市场份额约19%
   - 核心产品：Cobas系列，年收入约$15.6亿美元
   - 技术优势：免疫诊断、分子诊断
   
2. **Abbott Diagnostics**：市场份额约15%
   - 核心产品：Architect、Alinity系列
   - 技术优势：POCT、心血管标志物

3. **Siemens Healthineers**：市场份额约12%
   - 核心产品：Atellica、ADVIA系列
   - 技术优势：实验室自动化

4. **Danaher (Beckman Coulter)**：市场份额约10%
   - 核心产品：DxH、AU系列
   - 技术优势：血液学、生化诊断

### 新兴技术公司
- **Illumina**：NGS领域领导者，市值约$200亿美元
- **10x Genomics**：单细胞分析，年收入约$6亿美元
- **Guardant Health**：液体活检，年收入约$6.3亿美元

## 技术发展趋势

### 分子诊断增长驱动
- **NGS成本下降**：从2007年$1000万/基因组降至2023年<$1000/基因组
- **POCT分子诊断**：年增长率约12%，市场规模预计2030年达$89亿美元
- **液体活检**：年增长率约20%，2030年市场规模预计$74亿美元

### 人工智能应用
- **AI辅助诊断**：准确率提升15-30%
- **自动化实验室**：效率提升40-60%
- **预测性维护**：设备停机时间减少25%

## 投资并购活动

### 2023年重大交易
- **Danaher收购Aldevron**：$93亿美元，基因治疗CDMO
- **Thermo Fisher收购PPD**：$174亿美元，CRO服务
- **Roche收购GenMark**：$18亿美元，分子诊断

### 投资热点
- **液体活检**：2023年融资总额约$28亿美元
- **POCT技术**：2023年融资总额约$15亿美元
- **AI诊断**：2023年融资总额约$12亿美元"""
        
        # 创建临床应用知识库
        print("7. 创建临床应用知识库...")
        clinical_knowledge = """# IVD临床应用知识库

## 疾病诊断应用

### 感染性疾病诊断
- **新冠病毒检测**：
  - RT-PCR：敏感性95-99%，特异性>99%
  - 抗原检测：敏感性80-95%，特异性>95%
  - 抗体检测：用于流行病学调查

- **细菌感染诊断**：
  - 血培养：金标准，但需24-48小时
  - PCT (降钙素原)：细菌感染标志物，正常值<0.25 ng/mL
  - CRP：炎症标志物，正常值<3 mg/L

### 心血管疾病诊断
- **急性心肌梗死**：
  - cTnI/cTnT：99th百分位值作为诊断切点
  - CK-MB：传统标志物，敏感性较低
  - 肌红蛋白：早期标志物，特异性较低

- **心力衰竭**：
  - NT-proBNP：<125 pg/mL排除心衰
  - BNP：<100 pg/mL排除心衰

### 肿瘤标志物
- **肺癌**：
  - CEA：正常值<5 ng/mL
  - CYFRA21-1：鳞癌标志物
  - NSE：小细胞肺癌标志物

- **肝癌**：
  - AFP：>400 ng/mL高度提示肝癌
  - AFP-L3：特异性更高的异构体

## 个性化医疗应用

### 药物基因组学
- **CYP2D6基因型**：影响约25%药物代谢
- **CYP2C19基因型**：氯吡格雷疗效预测
- **TPMT基因型**：硫嘌呤类药物毒性预测

### 肿瘤精准治疗
- **EGFR突变检测**：肺癌靶向治疗指导
- **HER2扩增检测**：乳腺癌治疗选择
- **PD-L1表达检测**：免疫治疗疗效预测

## 质量控制要求

### 室内质控
- **Westgard规则**：
  - 1-2s：警告规则
  - 1-3s：失控规则
  - 2-2s：精密度失控
  - R-4s：随机误差
  - 4-1s：系统误差

### 室间质评
- **CAP调查**：美国病理学家学会质评项目
- **NCCLS标准**：质评样本制备和评价标准
- **可接受性能**：通常要求80%以上实验室结果在可接受范围内

## 临床决策支持

### 参考区间建立
- **样本量要求**：至少120个健康个体
- **统计方法**：非参数法（2.5-97.5百分位数）
- **分组考虑**：年龄、性别、种族等因素

### 临床意义解释
- **敏感性**：检出真正患者的能力
- **特异性**：排除非患者的能力
- **预测值**：与疾病患病率相关
- **似然比**：诊断价值的综合指标"""
        
        # 注册IVD专家agent
        print("8. 注册IVD专家agent...")
        agent_name = "ivd_expert"
        
        # 检查agent是否已存在
        if agent_name in pm.list_agents():
            print(f"   警告：Agent '{agent_name}' 已存在，正在删除...")
            pm.delete_agent(agent_name)
            print(f"   已删除现有agent '{agent_name}'")
        
        pm.register_new_agent(
            agent_name=agent_name,
            system_message=system_message,
            user_message=user_message,
            technical_knowledge=technical_knowledge,
            regulatory_knowledge=regulatory_knowledge,
            market_knowledge=market_knowledge,
            clinical_knowledge=clinical_knowledge
        )
        print(f"   Agent '{agent_name}' 注册成功！\n")
        
        # 验证agent创建
        print("9. 验证agent创建...")
        agents = pm.list_agents()
        print(f"   当前agents: {agents}")
        
        if agent_name in agents:
            info = pm.get_agent_info(agent_name)
            print(f"   Agent信息: {info}\n")
            
            # 获取prompts信息
            prompts = pm.get_prompts(agent_name)
            print(f"   系统模板变量: {prompts['system'].input_variables}")
            print(f"   用户模板变量: {prompts['user'].input_variables}\n")
            
            print("=== IVD专家Agent创建成功！ ===")
            print(f"Agent名称: {agent_name}")
            print(f"存储路径: {pm.store_path / agent_name}")
            print(f"知识模块: {len(info['variables'])} 个")
            print("\n可用的知识模块:")
            for var in info['variables']:
                print(f"  - {var}")
                
        else:
            print("   错误：Agent创建失败")
            
    except AgentAlreadyExistsError as e:
        print(f"Agent已存在错误: {e}")
        if hasattr(e, 'context'):
            print(f"上下文: {e.context}")
    
    except PromptConfigurationError as e:
        print(f"配置错误: {e}")
        if hasattr(e, 'context'):
            print(f"上下文: {e.context}")
    
    except Exception as e:
        print(f"意外错误: {e}")
        import traceback
        traceback.print_exc()


def test_ivd_expert_agent():
    """测试IVD专家agent"""
    
    print("\n=== 测试IVD专家Agent ===\n")
    
    try:
        # 初始化PromptManager
        pm = PromptManager("./ivd_prompt_store")
        
        # 获取agent prompts
        prompts = pm.get_prompts("ivd_expert")
        
        # 测试系统提示词
        print("1. 测试系统提示词...")
        system_prompt = prompts['system'].format()
        print(f"   系统提示词长度: {len(system_prompt)} 字符")
        print(f"   前200字符预览: {system_prompt[:200]}...\n")
        
        # 测试用户提示词
        print("2. 测试用户提示词...")
        user_prompt = prompts['user'].format(
            question_type="技术分析",
            user_question="请分析qPCR和dPCR技术的优缺点及适用场景",
            context_info="用于新产品开发的技术选型"
        )
        print(f"   用户提示词长度: {len(user_prompt)} 字符")
        print(f"   格式化成功！\n")
        
        print("=== 测试完成，Agent可正常使用！ ===")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 创建IVD专家agent
    create_ivd_expert_agent()
    
    # 测试agent功能
    test_ivd_expert_agent()
    
    print("\n" + "="*50)
    print("IVD专家Agent已成功创建并测试完成！")
    print("您现在可以使用这个专业的IVD领域专家进行咨询。")
    print("="*50)
