# IVD分析评价专家Agent

## 项目概述

本项目基于prompt_manager框架创建了一个专业的IVD分析评价专家agent，专门用于客观、中立地评价IVD相关分析报告的质量。该agent与IVD专家agent形成完整的质量控制闭环，确保分析的科学严谨性和实用价值。

## 核心特性

### 🎯 客观中立
- **无商业偏见**：不偏向任何特定技术平台或商业利益
- **基于证据**：所有评价基于可验证的科学证据和数据
- **平衡视角**：同时考虑优势和局限性，避免过度乐观或悲观
- **透明标准**：评价标准公开透明，可重现和验证

### 🔍 批判性思维
- **逻辑一致性**：检查论证的逻辑链条是否完整
- **证据充分性**：评估支撑结论的证据是否充分可靠
- **假设合理性**：识别隐含假设并评估其合理性
- **替代解释**：考虑是否存在其他合理的解释

### 📊 科学严谨性
- **统计学正确性**：验证统计方法的适用性和准确性
- **实验设计合理性**：评估研究设计是否支撑结论
- **数据质量评估**：检查数据来源的可靠性和代表性
- **不确定性量化**：识别和量化分析中的不确定性

## 与IVD专家Agent的差异

| 维度 | IVD专家Agent | IVD评价专家Agent |
|------|-------------|-----------------|
| **角色定位** | 顾问型专家，提供建议 | 审查型专家，评估质量 |
| **工作目标** | 生成专业分析和解决方案 | 评价分析质量和可信度 |
| **立场态度** | 专业建议，相对积极 | 客观中立，批判性思维 |
| **输出内容** | 技术分析、市场洞察等 | 质量评分、问题诊断等 |
| **知识重点** | 专业知识和最佳实践 | 评价标准和质量控制 |

## 文件结构

```
expert_agent/
├── create_ivd_evaluator_agent.py      # 创建评价专家agent的脚本
├── test_ivd_agents_collaboration.py   # 测试两个agent协同工作
├── use_ivd_agents_example.py          # 完整使用示例
├── ivd_evaluator_system_prompt.md     # 评价专家系统提示词
├── ivd_evaluator_user_prompt.md       # 评价专家用户提示词
├── ivd_evaluation_standards.md        # 评价标准知识库
├── ivd_common_issues.md               # 常见问题识别知识库
└── ivd_prompt_store/                  # agent存储目录
    ├── ivd_expert/                    # IVD专家agent
    └── ivd_evaluator/                 # IVD评价专家agent
        ├── config.json                # 配置文件
        ├── system_message.md          # 系统消息
        ├── user_message.md            # 用户消息模板
        ├── evaluation_standards.md    # 评价标准模块
        ├── common_issues.md           # 常见问题模块
        ├── evaluation_methodology.md  # 评价方法论模块
        └── evaluation_cases.md        # 评价案例模块
```

## 知识模块详解

### 1. 评价标准模块 (evaluation_standards.md)
- **科学严谨性标准**：统计学严谨性、实验设计、数据质量
- **证据充分性标准**：文献证据评估、引用质量、数据来源
- **客观中立性标准**：偏见识别、利益冲突、平衡性评估
- **完整性标准**：技术、法规、市场、临床分析完整性
- **质量评分标准**：9-10分制评分体系和关键指标

### 2. 常见问题识别模块 (common_issues.md)
- **技术分析问题**：过度乐观、数据不实、比较偏颇等
- **法规分析问题**：信息过时、流程简化、风险忽视等
- **市场分析问题**：数据不可靠、预测过于乐观、竞争分析不足等
- **临床分析问题**：价值夸大、证据质量低、成本效益简化等
- **问题严重程度分级**：严重、中等、轻微三个等级

### 3. 评价方法论模块 (evaluation_methodology.md)
- **循证评价方法**：GRADE体系、系统评价方法
- **批判性评价框架**：内部效度、外部效度评估
- **定量评价方法**：诊断准确性、经济评价方法
- **质量评价工具**：QUADAS-2、CHEERS清单等
- **偏倚识别与控制**：认知偏倚识别、控制方法

### 4. 评价案例模块 (evaluation_cases.md)
- **优秀分析案例特征**：全面性、准确性、客观性、深度性
- **常见问题案例**：具体的问题示例和识别方法
- **评价标准应用案例**：高分和低分案例对比
- **改进建议案例**：结构性、内容性、方法性改进
- **质量控制案例**：一致性、重现性、有效性验证

## 评价维度和标准

### 评价维度（权重）
1. **科学严谨性**（25%）- 统计方法、实验设计、数据质量
2. **证据充分性**（25%）- 文献质量、引用相关性、数据来源
3. **客观中立性**（20%）- 偏见控制、平衡表达、利益冲突
4. **完整性**（20%）- 覆盖全面性、关键要素、风险识别
5. **实用价值**（10%）- 可操作性、实际意义、决策支持

### 评分等级
- **9-10分（优秀）**：达到国际顶级期刊发表标准
- **7-8分（良好）**：达到主流期刊发表标准
- **5-6分（合格）**：基本满足专业分析要求
- **3-4分（需改进）**：存在明显缺陷，需要改进
- **1-2分（不合格）**：严重缺陷，不建议使用

## 快速开始

### 1. 创建评价专家Agent

```bash
python create_ivd_evaluator_agent.py
```

### 2. 测试协同工作

```bash
python test_ivd_agents_collaboration.py
```

### 3. 使用完整工作流程

```bash
python use_ivd_agents_example.py
```

## 使用方法

### 标准工作流程

```python
from prompt_manager import PromptManager

# 初始化
pm = PromptManager("./ivd_prompt_store")

# 第一步：生成专家分析
expert_prompts = pm.get_prompts("ivd_expert")
expert_system = expert_prompts['system'].format()
expert_user = expert_prompts['user'].format(
    question_type="技术分析",
    user_question="您的问题",
    context_info="背景信息"
)

# 第二步：获得AI模型分析结果
# analysis_result = ai_model.chat(expert_system, expert_user)

# 第三步：生成评价提示词
evaluator_prompts = pm.get_prompts("ivd_evaluator")
eval_system = evaluator_prompts['system'].format()
eval_user = evaluator_prompts['user'].format(
    original_question_type="技术分析",
    original_question="您的问题",
    original_context="背景信息",
    analysis_content=analysis_result
)

# 第四步：获得质量评价
# evaluation_result = ai_model.chat(eval_system, eval_user)
```

## 评价输出格式

### 标准评价报告结构
1. **执行摘要**：总体质量水平、主要优势和核心问题
2. **优势分析**：分析的亮点和价值
3. **问题诊断**：具体问题和严重程度
4. **改进建议**：针对性的改进方案
5. **量化评估**：各维度评分和总体评级
6. **置信度评估**：评价结论的可信度
7. **使用建议**：适用场景和使用限制

### 评价特征
- **客观描述**：使用中性语言，避免主观判断
- **具体指向**：明确指出问题所在的段落或论点
- **建设性批评**：提供改进建议而非单纯批评
- **平衡表达**：既指出问题也认可优势

## 应用场景

### 🏢 企业质量控制
- **内部审查**：分析报告的内部质量控制
- **决策支持**：重要决策前的分析质量评估
- **培训改进**：识别分析能力的薄弱环节
- **标准建立**：建立企业内部分析质量标准

### 🎓 学术研究评审
- **论文评审**：学术论文的质量评估
- **项目评估**：研究项目的技术可行性评估
- **同行评议**：专业同行评议的辅助工具
- **教学改进**：学生分析能力的评估和指导

### 💼 投资决策支持
- **尽职调查**：投资项目的技术分析评估
- **风险识别**：技术和市场风险的识别
- **价值评估**：技术价值和商业价值的客观评估
- **决策优化**：基于高质量分析的投资决策

### 🏥 临床应用评估
- **技术引进**：新技术引进的评估
- **设备采购**：医疗设备采购的技术评估
- **临床研究**：临床研究方案的质量评估
- **指南制定**：临床指南制定的证据评估

## 质量保证机制

### 评价质量控制
- **多重验证**：关键评价结论的多重验证
- **标准校准**：定期校准评价标准和方法
- **一致性检查**：评价者间一致性验证
- **持续改进**：基于反馈的持续改进

### 伦理和职业操守
- **独立性原则**：保持评价的独立性和客观性
- **保密原则**：严格保护被评价内容的机密信息
- **专业发展**：持续跟踪领域发展，更新评价方法

## 最佳实践

### 使用建议
1. **详细背景**：提供充分的原始问题背景信息
2. **完整内容**：提供完整的待评价分析内容
3. **多轮评价**：对重要分析进行多轮质量评价
4. **专家验证**：结合领域专家意见进行最终验证

### 注意事项
⚠️ **重要提醒**：
- 评价结果仅供参考，重要决策需结合专家意见
- 定期更新知识库以保持评价标准的时效性
- 对于关键应用，建议进行人工专家复核
- 评价质量依赖于AI模型的性能和训练质量

## 技术特点

### 🔧 基于prompt_manager框架
- **模块化设计**：评价标准独立管理，易于更新
- **模板化生成**：支持变量替换和动态内容
- **版本控制**：配置文件管理，支持agent重载

### 📚 专业评价体系
- **4个核心模块**：标准、问题、方法、案例
- **10000+字符**：深度专业评价知识
- **科学方法**：基于循证医学和质量管理理论

### 🎯 质量控制闭环
- **双agent协同**：专家分析 + 质量评价
- **标准化流程**：规范的评价流程和输出格式
- **持续改进**：基于评价结果的质量提升

---

**版本信息**：v1.0  
**创建日期**：2024年  
**适用范围**：IVD行业质量控制、学术研究评审、投资决策支持
