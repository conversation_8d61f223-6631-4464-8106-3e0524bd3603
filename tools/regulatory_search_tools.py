#!/usr/bin/env python3
"""
IVD专家Agent搜索工具函数

这个模块提供了一系列工具函数，用于支持IVD专家和评价专家的分析工作。
包括法规搜索、文献检索、数据查询等功能。
"""

import os
import re
import json
import sqlite3
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import requests
from dataclasses import dataclass


@dataclass
class SearchResult:
    """搜索结果数据类"""
    title: str
    content: str
    source: str
    relevance_score: float
    metadata: Dict[str, Any] = None


class RegulatorySearchTools:
    """法规搜索工具类"""
    
    def __init__(self, regulatory_db_path: str = "./regulatory_database"):
        """初始化搜索工具"""
        self.regulatory_db_path = Path(regulatory_db_path)
        self.db_path = self.regulatory_db_path / "regulatory.db"
        self._init_database()
        self._load_regulatory_documents()
    
    def _init_database(self):
        """初始化SQLite数据库"""
        self.regulatory_db_path.mkdir(exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建法规文档表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS regulatory_docs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                regulation_name TEXT NOT NULL,
                section TEXT,
                title TEXT,
                content TEXT,
                keywords TEXT,
                last_updated DATE
            )
        ''')
        
        # 创建标准文档表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS standards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                standard_number TEXT NOT NULL,
                title TEXT,
                section TEXT,
                content TEXT,
                status TEXT,
                publication_date DATE
            )
        ''')
        
        # 创建指导原则表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS guidance_docs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agency TEXT,
                document_title TEXT,
                document_number TEXT,
                content TEXT,
                publication_date DATE,
                status TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def _load_regulatory_documents(self):
        """加载法规文档到数据库"""
        regulatory_files = [
            ("ISO 13485:2016", "iso_13485_2016.md"),
            ("FDA 21 CFR Part 820", "fda_21_cfr_part_820.md"),
            ("EU IVDR 2017/746", "eu_ivdr_2017_746.md")
        ]
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for reg_name, filename in regulatory_files:
            file_path = self.regulatory_db_path / filename
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 解析文档结构
                sections = self._parse_markdown_sections(content)
                
                for section_title, section_content in sections.items():
                    # 检查是否已存在
                    cursor.execute('''
                        SELECT id FROM regulatory_docs 
                        WHERE regulation_name = ? AND section = ?
                    ''', (reg_name, section_title))
                    
                    if not cursor.fetchone():
                        # 提取关键词
                        keywords = self._extract_keywords(section_content)
                        
                        cursor.execute('''
                            INSERT INTO regulatory_docs 
                            (regulation_name, section, title, content, keywords, last_updated)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ''', (reg_name, section_title, section_title, section_content, 
                             ', '.join(keywords), datetime.now().date()))
        
        conn.commit()
        conn.close()
    
    def _parse_markdown_sections(self, content: str) -> Dict[str, str]:
        """解析Markdown文档的章节"""
        sections = {}
        current_section = ""
        current_content = []
        
        lines = content.split('\n')
        for line in lines:
            if line.startswith('#'):
                if current_section and current_content:
                    sections[current_section] = '\n'.join(current_content)
                
                current_section = line.strip('#').strip()
                current_content = []
            else:
                current_content.append(line)
        
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content)
        
        return sections
    
    def _extract_keywords(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        # 简单的关键词提取逻辑
        keywords = []
        
        # IVD相关关键词
        ivd_keywords = [
            'quality management', 'design control', 'risk management',
            'clinical performance', 'analytical performance', 'validation',
            'verification', 'traceability', 'CAPA', 'corrective action',
            'preventive action', 'post-market surveillance', 'vigilance',
            'notified body', 'conformity assessment', 'CE marking',
            'FDA approval', '510(k)', 'PMA', 'classification', 'UDI'
        ]
        
        text_lower = text.lower()
        for keyword in ivd_keywords:
            if keyword.lower() in text_lower:
                keywords.append(keyword)
        
        return keywords[:10]  # 限制关键词数量
    
    def search_regulation(self, query: str, regulation_name: str = None) -> List[SearchResult]:
        """搜索法规内容"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 构建SQL查询
        sql = '''
            SELECT regulation_name, section, title, content, keywords
            FROM regulatory_docs
            WHERE content LIKE ?
        '''
        params = [f'%{query}%']
        
        if regulation_name:
            sql += ' AND regulation_name = ?'
            params.append(regulation_name)
        
        sql += ' ORDER BY regulation_name, section'
        
        cursor.execute(sql, params)
        results = cursor.fetchall()
        conn.close()
        
        search_results = []
        for result in results:
            reg_name, section, title, content, keywords = result
            
            # 计算相关性分数
            relevance_score = self._calculate_relevance(query, content)
            
            search_results.append(SearchResult(
                title=f"{reg_name} - {section}",
                content=content[:500] + "..." if len(content) > 500 else content,
                source=reg_name,
                relevance_score=relevance_score,
                metadata={
                    "section": section,
                    "keywords": keywords,
                    "regulation": reg_name
                }
            ))
        
        # 按相关性排序
        search_results.sort(key=lambda x: x.relevance_score, reverse=True)
        return search_results[:10]  # 返回前10个结果
    
    def _calculate_relevance(self, query: str, content: str) -> float:
        """计算搜索相关性分数"""
        query_words = query.lower().split()
        content_lower = content.lower()
        
        score = 0.0
        for word in query_words:
            # 精确匹配得分更高
            exact_matches = content_lower.count(word)
            score += exact_matches * 2
            
            # 部分匹配
            partial_matches = len([w for w in content_lower.split() if word in w])
            score += partial_matches * 0.5
        
        # 标准化分数
        return min(score / len(content_lower.split()) * 100, 100.0)
    
    def get_regulation_section(self, regulation_name: str, section_name: str) -> Optional[SearchResult]:
        """获取特定法规的特定章节"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT regulation_name, section, title, content, keywords
            FROM regulatory_docs
            WHERE regulation_name = ? AND section LIKE ?
        ''', (regulation_name, f'%{section_name}%'))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            reg_name, section, title, content, keywords = result
            return SearchResult(
                title=f"{reg_name} - {section}",
                content=content,
                source=reg_name,
                relevance_score=100.0,
                metadata={
                    "section": section,
                    "keywords": keywords,
                    "regulation": reg_name
                }
            )
        return None
    
    def list_available_regulations(self) -> List[str]:
        """列出可用的法规"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT DISTINCT regulation_name FROM regulatory_docs')
        results = cursor.fetchall()
        conn.close()
        
        return [result[0] for result in results]
    
    def get_regulation_sections(self, regulation_name: str) -> List[str]:
        """获取特定法规的所有章节"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT section FROM regulatory_docs
            WHERE regulation_name = ?
            ORDER BY section
        ''', (regulation_name,))
        
        results = cursor.fetchall()
        conn.close()
        
        return [result[0] for result in results]


class LiteratureSearchTools:
    """文献搜索工具类"""
    
    def __init__(self):
        """初始化文献搜索工具"""
        self.pubmed_base_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/"
    
    def search_pubmed(self, query: str, max_results: int = 10) -> List[SearchResult]:
        """搜索PubMed文献"""
        try:
            # 搜索文献ID
            search_url = f"{self.pubmed_base_url}esearch.fcgi"
            search_params = {
                'db': 'pubmed',
                'term': query,
                'retmax': max_results,
                'retmode': 'json'
            }
            
            response = requests.get(search_url, params=search_params, timeout=10)
            search_data = response.json()
            
            if 'esearchresult' not in search_data or 'idlist' not in search_data['esearchresult']:
                return []
            
            pmids = search_data['esearchresult']['idlist']
            
            if not pmids:
                return []
            
            # 获取文献详情
            fetch_url = f"{self.pubmed_base_url}efetch.fcgi"
            fetch_params = {
                'db': 'pubmed',
                'id': ','.join(pmids),
                'retmode': 'xml'
            }
            
            response = requests.get(fetch_url, params=fetch_params, timeout=10)
            
            # 简化处理，返回基本信息
            results = []
            for i, pmid in enumerate(pmids):
                results.append(SearchResult(
                    title=f"PubMed Article {pmid}",
                    content=f"PubMed ID: {pmid}\nQuery: {query}",
                    source="PubMed",
                    relevance_score=100.0 - i * 5,  # 简单的相关性评分
                    metadata={"pmid": pmid, "query": query}
                ))
            
            return results
            
        except Exception as e:
            print(f"PubMed search error: {e}")
            return []
    
    def search_ivd_literature(self, topic: str) -> List[SearchResult]:
        """搜索IVD相关文献"""
        # 构建IVD特定的搜索查询
        ivd_query = f"({topic}) AND (in vitro diagnostic OR IVD OR medical device)"
        return self.search_pubmed(ivd_query)


class MarketDataTools:
    """市场数据工具类"""
    
    def __init__(self):
        """初始化市场数据工具"""
        self.market_data = self._load_market_data()
    
    def _load_market_data(self) -> Dict[str, Any]:
        """加载市场数据"""
        # 模拟市场数据
        return {
            "global_ivd_market": {
                "2023": {"size_billion_usd": 102.1, "growth_rate": 6.1},
                "2024": {"size_billion_usd": 108.3, "growth_rate": 6.1},
                "2030_forecast": {"size_billion_usd": 154.3}
            },
            "regional_markets": {
                "north_america": {"share_percent": 40, "growth_rate": 5.8},
                "europe": {"share_percent": 30, "growth_rate": 5.5},
                "asia_pacific": {"share_percent": 25, "growth_rate": 8.2},
                "others": {"share_percent": 5, "growth_rate": 4.5}
            },
            "technology_segments": {
                "molecular_diagnostics": {"share_percent": 25, "growth_rate": 8.5},
                "immunoassays": {"share_percent": 35, "growth_rate": 5.2},
                "clinical_chemistry": {"share_percent": 20, "growth_rate": 4.8},
                "hematology": {"share_percent": 10, "growth_rate": 4.2},
                "others": {"share_percent": 10, "growth_rate": 6.0}
            }
        }
    
    def get_market_size(self, year: int = 2023) -> Dict[str, Any]:
        """获取市场规模数据"""
        if str(year) in self.market_data["global_ivd_market"]:
            return self.market_data["global_ivd_market"][str(year)]
        return {"error": f"No data available for year {year}"}
    
    def get_regional_data(self, region: str = None) -> Dict[str, Any]:
        """获取区域市场数据"""
        if region:
            region_key = region.lower().replace(" ", "_")
            if region_key in self.market_data["regional_markets"]:
                return self.market_data["regional_markets"][region_key]
            return {"error": f"No data available for region {region}"}
        return self.market_data["regional_markets"]
    
    def get_technology_trends(self, technology: str = None) -> Dict[str, Any]:
        """获取技术趋势数据"""
        if technology:
            tech_key = technology.lower().replace(" ", "_")
            if tech_key in self.market_data["technology_segments"]:
                return self.market_data["technology_segments"][tech_key]
            return {"error": f"No data available for technology {technology}"}
        return self.market_data["technology_segments"]


# 工具函数装饰器，用于AutoGen集成
def regulatory_search_tool(query: str, regulation: str = None) -> str:
    """
    搜索法规内容的工具函数
    
    Args:
        query: 搜索查询词
        regulation: 特定法规名称（可选）
    
    Returns:
        搜索结果的JSON字符串
    """
    tools = RegulatorySearchTools()
    results = tools.search_regulation(query, regulation)
    
    formatted_results = []
    for result in results:
        formatted_results.append({
            "title": result.title,
            "content": result.content,
            "source": result.source,
            "relevance": result.relevance_score
        })
    
    return json.dumps(formatted_results, ensure_ascii=False, indent=2)


def literature_search_tool(topic: str) -> str:
    """
    搜索IVD相关文献的工具函数
    
    Args:
        topic: 搜索主题
    
    Returns:
        文献搜索结果的JSON字符串
    """
    tools = LiteratureSearchTools()
    results = tools.search_ivd_literature(topic)
    
    formatted_results = []
    for result in results:
        formatted_results.append({
            "title": result.title,
            "content": result.content,
            "source": result.source,
            "pmid": result.metadata.get("pmid") if result.metadata else None
        })
    
    return json.dumps(formatted_results, ensure_ascii=False, indent=2)


def market_data_tool(data_type: str, parameter: str = None) -> str:
    """
    获取市场数据的工具函数
    
    Args:
        data_type: 数据类型 (market_size, regional_data, technology_trends)
        parameter: 参数（年份、地区、技术等）
    
    Returns:
        市场数据的JSON字符串
    """
    tools = MarketDataTools()
    
    if data_type == "market_size":
        year = int(parameter) if parameter and parameter.isdigit() else 2023
        data = tools.get_market_size(year)
    elif data_type == "regional_data":
        data = tools.get_regional_data(parameter)
    elif data_type == "technology_trends":
        data = tools.get_technology_trends(parameter)
    else:
        data = {"error": f"Unknown data type: {data_type}"}
    
    return json.dumps(data, ensure_ascii=False, indent=2)


def get_regulation_section_tool(regulation: str, section: str) -> str:
    """
    获取特定法规章节的工具函数
    
    Args:
        regulation: 法规名称
        section: 章节名称
    
    Returns:
        法规章节内容的JSON字符串
    """
    tools = RegulatorySearchTools()
    result = tools.get_regulation_section(regulation, section)
    
    if result:
        return json.dumps({
            "title": result.title,
            "content": result.content,
            "source": result.source,
            "metadata": result.metadata
        }, ensure_ascii=False, indent=2)
    else:
        return json.dumps({
            "error": f"Section '{section}' not found in regulation '{regulation}'"
        }, ensure_ascii=False, indent=2)


# 可用工具函数列表
AVAILABLE_TOOLS = [
    {
        "name": "regulatory_search_tool",
        "description": "Search regulatory documents and standards",
        "function": regulatory_search_tool
    },
    {
        "name": "literature_search_tool", 
        "description": "Search IVD-related literature from PubMed",
        "function": literature_search_tool
    },
    {
        "name": "market_data_tool",
        "description": "Get IVD market data and trends",
        "function": market_data_tool
    },
    {
        "name": "get_regulation_section_tool",
        "description": "Get specific section from a regulation",
        "function": get_regulation_section_tool
    }
]


if __name__ == "__main__":
    # 测试工具函数
    print("Testing regulatory search tools...")
    
    # 测试法规搜索
    result = regulatory_search_tool("design control", "ISO 13485:2016")
    print("Regulatory search result:")
    print(result[:500] + "..." if len(result) > 500 else result)
    
    # 测试市场数据
    result = market_data_tool("market_size", "2023")
    print("\nMarket data result:")
    print(result)
