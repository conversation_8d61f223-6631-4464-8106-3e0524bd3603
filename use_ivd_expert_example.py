#!/usr/bin/env python3
"""
IVD专家Agent使用示例

这个脚本展示如何使用创建的IVD专家agent进行专业咨询。
在实际应用中，您需要将生成的提示词发送给AI模型（如GPT-4、<PERSON>等）。
"""

import sys
from pathlib import Path

# 添加prompt_manager到Python路径
sys.path.append(str(Path(__file__).parent / "prompt_manager"))

from prompt_manager import PromptManager


class IVDExpertConsultant:
    """IVD专家咨询助手"""
    
    def __init__(self, prompt_store_path="./ivd_prompt_store"):
        """初始化IVD专家咨询助手"""
        self.pm = PromptManager(prompt_store_path)
        self.prompts = self.pm.get_prompts("ivd_expert")
        
    def get_expert_prompt(self, question_type, user_question, context_info=""):
        """
        生成专家咨询提示词
        
        Args:
            question_type: 问题类型（技术分析、法规合规性、市场和商业分析、临床应用价值）
            user_question: 用户具体问题
            context_info: 背景信息（可选）
            
        Returns:
            tuple: (system_prompt, user_prompt)
        """
        system_prompt = self.prompts['system'].format()
        user_prompt = self.prompts['user'].format(
            question_type=question_type,
            user_question=user_question,
            context_info=context_info
        )
        
        return system_prompt, user_prompt
    
    def print_consultation_prompt(self, question_type, user_question, context_info=""):
        """打印咨询提示词（用于发送给AI模型）"""
        
        system_prompt, user_prompt = self.get_expert_prompt(
            question_type, user_question, context_info
        )
        
        print("="*80)
        print("IVD专家咨询提示词")
        print("="*80)
        print("\n【系统提示词】")
        print("-"*40)
        print(system_prompt)
        print("\n【用户提示词】")
        print("-"*40)
        print(user_prompt)
        print("\n" + "="*80)
        
        return system_prompt, user_prompt


def demo_technical_analysis():
    """演示技术分析咨询"""
    
    print("\n🔬 技术分析咨询演示")
    print("="*50)
    
    consultant = IVDExpertConsultant()
    
    question_type = "技术分析"
    user_question = """
    请详细分析CRISPR-Cas诊断技术（如SHERLOCK、DETECTR）的技术原理、
    性能特点和在病原体检测中的应用前景。与传统PCR技术相比有哪些优势和局限性？
    """
    
    context_info = """
    我们公司正在评估CRISPR诊断技术用于开发新型病原体检测产品。
    目标应用场景包括：
    1. 新发传染病快速检测
    2. 多重病原体同时检测
    3. 现场即时检测(POCT)
    
    技术要求：
    - 检测时间：<30分钟
    - 敏感性：≥95%
    - 特异性：≥99%
    - 可检测病原体数量：≥10种
    """
    
    system_prompt, user_prompt = consultant.print_consultation_prompt(
        question_type, user_question, context_info
    )
    
    print("\n💡 使用提示：")
    print("将上述系统提示词和用户提示词发送给AI模型（如GPT-4、Claude等）")
    print("即可获得专业的IVD技术分析。")


def demo_regulatory_consultation():
    """演示法规咨询"""
    
    print("\n📋 法规咨询演示")
    print("="*50)
    
    consultant = IVDExpertConsultant()
    
    question_type = "法规合规性"
    user_question = """
    我们的NGS肿瘤基因检测产品准备申请NMPA三类医疗器械注册，
    请分析注册流程、临床试验设计要点、技术审评重点和可能遇到的挑战。
    """
    
    context_info = """
    产品信息：
    - 产品名称：肿瘤组织基因突变检测试剂盒（NGS法）
    - 检测基因：450个肿瘤相关基因
    - 样本类型：FFPE组织样本
    - 预期用途：肿瘤精准治疗的伴随诊断
    - 目标科室：肿瘤科、病理科
    
    公司情况：
    - 已有ISO 13485质量体系认证
    - 具备NGS实验室和生物信息分析能力
    - 团队包括分子生物学、生物信息学、法规事务专家
    """
    
    consultant.print_consultation_prompt(
        question_type, user_question, context_info
    )


def demo_market_analysis():
    """演示市场分析咨询"""
    
    print("\n📊 市场分析咨询演示")
    print("="*50)
    
    consultant = IVDExpertConsultant()
    
    question_type = "市场和商业分析"
    user_question = """
    请分析中国POCT市场的发展现状、竞争格局和投资机会，
    特别关注心血管标志物检测领域的市场前景和技术趋势。
    """
    
    context_info = """
    投资背景：
    - 我们是一家专注医疗健康的投资基金
    - 关注早期到成长期的IVD公司
    - 投资规模：单项目500万-5000万人民币
    - 重点关注技术创新和市场潜力
    
    分析需求：
    - 市场规模和增长预测
    - 主要参与者和竞争态势
    - 技术发展趋势和壁垒
    - 投资风险和机会评估
    - 退出路径分析
    """
    
    consultant.print_consultation_prompt(
        question_type, user_question, context_info
    )


def demo_clinical_application():
    """演示临床应用咨询"""
    
    print("\n🏥 临床应用咨询演示")
    print("="*50)
    
    consultant = IVDExpertConsultant()
    
    question_type = "临床应用价值"
    user_question = """
    请评估多组学检测技术在阿尔茨海默病早期诊断中的临床价值，
    包括技术可行性、诊断性能、成本效益和实施挑战。
    """
    
    context_info = """
    临床背景：
    - 某三甲医院神经内科正在建设记忆门诊
    - 目标人群：65岁以上认知功能下降患者
    - 预计年检测量：2000-3000例
    - 现有诊断手段：认知量表、影像学检查、脑脊液检测
    
    技术考虑：
    - 血液多组学检测（蛋白质组+代谢组+基因组）
    - 检测成本控制在2000-5000元/例
    - 检测周期：7-14天
    - 诊断准确性要求：敏感性>85%，特异性>90%
    """
    
    consultant.print_consultation_prompt(
        question_type, user_question, context_info
    )


def interactive_consultation():
    """交互式咨询演示"""
    
    print("\n🤖 交互式咨询演示")
    print("="*50)
    
    consultant = IVDExpertConsultant()
    
    print("请选择咨询类型：")
    print("1. 技术分析")
    print("2. 法规合规性")
    print("3. 市场和商业分析")
    print("4. 临床应用价值")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    question_types = {
        "1": "技术分析",
        "2": "法规合规性", 
        "3": "市场和商业分析",
        "4": "临床应用价值"
    }
    
    if choice not in question_types:
        print("无效选择，使用默认类型：技术分析")
        choice = "1"
    
    question_type = question_types[choice]
    
    print(f"\n您选择了：{question_type}")
    print("\n请输入您的问题：")
    user_question = input().strip()
    
    print("\n请输入背景信息（可选，直接回车跳过）：")
    context_info = input().strip()
    
    if not user_question:
        print("未输入问题，使用演示问题...")
        user_question = "请介绍IVD行业的发展趋势和技术创新方向。"
    
    consultant.print_consultation_prompt(
        question_type, user_question, context_info
    )


def main():
    """主函数"""
    
    print("🧬 IVD专家Agent使用演示")
    print("="*60)
    print("这个演示展示如何使用IVD专家agent生成专业咨询提示词")
    print("生成的提示词可以发送给AI模型获得专业分析")
    print("="*60)
    
    demos = [
        ("技术分析咨询", demo_technical_analysis),
        ("法规咨询", demo_regulatory_consultation),
        ("市场分析咨询", demo_market_analysis),
        ("临床应用咨询", demo_clinical_application)
    ]
    
    print("\n可用的演示：")
    for i, (name, _) in enumerate(demos, 1):
        print(f"{i}. {name}")
    print(f"{len(demos)+1}. 交互式咨询")
    print(f"{len(demos)+2}. 运行所有演示")
    
    choice = input(f"\n请选择演示 (1-{len(demos)+2}): ").strip()
    
    if choice == str(len(demos)+1):
        interactive_consultation()
    elif choice == str(len(demos)+2):
        for name, demo_func in demos:
            demo_func()
    elif choice.isdigit() and 1 <= int(choice) <= len(demos):
        _, demo_func = demos[int(choice)-1]
        demo_func()
    else:
        print("无效选择，运行所有演示...")
        for name, demo_func in demos:
            demo_func()
    
    print("\n" + "="*60)
    print("演示完成！")
    print("\n📝 使用说明：")
    print("1. 复制生成的系统提示词和用户提示词")
    print("2. 发送给您选择的AI模型（GPT-4、Claude、文心一言等）")
    print("3. 获得专业的IVD领域分析和建议")
    print("\n🔧 集成说明：")
    print("- 可以将IVDExpertConsultant类集成到您的应用中")
    print("- 支持API调用和批量处理")
    print("- 可根据需要扩展更多专业领域")
    print("="*60)


if __name__ == "__main__":
    main()
