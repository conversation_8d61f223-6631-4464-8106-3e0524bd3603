# **Microsoft AutoGen 框架完整参考文档**

## **第一部分：核心摘要与设计哲学 (High-Level Summary & Philosophy)**

> **[Core Goal]** AutoGen 是微软开发的开源多智能体对话框架，旨在通过多个专门化的AI智能体协作对话来构建下一代大语言模型应用。其核心思想是将复杂任务分解给具有不同角色和专业能力的智能体（如程序员、测试员、分析师、用户代理等）协同完成。

> **[Key Mechanism]** AutoGen v0.4 采用**异步事件驱动架构**，智能体通过异步消息进行通信。工作流由**对话驱动**，支持从简单的两智能体对话到复杂的多智能体网络。关键特色包括**代码执行能力**、**实时观测性**、**跨语言支持**和**分布式部署**能力。

> **[Architecture Philosophy]** v0.4版本采用分层架构设计：Core层提供事件驱动基础、AgentChat层提供高级对话API、Extensions层支持第三方集成。这种设计确保了框架的可扩展性、模块化和生产就绪性。

---

## **第二部分：核心组件字典 (Core Components Dictionary)**

### **智能体类型 (Agent Types)**
| 组件/概念 | 核心实现类 | 职责与作用 |
| :--- | :--- | :--- |
| 助手智能体 | `autogen_agentchat.agents.AssistantAgent` | 基于LLM的智能体，负责生成文本、分析和推理，支持工具调用和多模态输入 |
| 用户代理智能体 | `autogen_agentchat.agents.UserProxyAgent` | 代表用户参与对话，支持人工输入和交互，可配置输入超时 |
| 代码执行智能体 | `autogen_agentchat.agents.CodeExecutorAgent` | 专门负责执行和验证代码，提供安全的执行环境 |
| 社会思维智能体 | `autogen_agentchat.agents.SocietyOfMindAgent` | 嵌套团队智能体，内部包含一个完整的智能体团队 |
| 自定义智能体基类 | `autogen_agentchat.agents.BaseChatAgent` | 所有智能体的基类，用于创建自定义智能体 |
| OpenAI助手智能体 | `autogen_ext.agents.openai.OpenAIAssistantAgent` | 基于OpenAI Assistant API的智能体，支持文件上传和自定义线程 |

### **团队/群聊类型 (Team/Group Chat Types)**
| 组件/概念 | 核心实现类 | 职责与作用 |
| :--- | :--- | :--- |
| 轮询群聊 | `autogen_agentchat.teams.RoundRobinGroupChat` | 按固定顺序轮流发言的群聊管理器 |
| 选择器群聊 | `autogen_agentchat.teams.SelectorGroupChat` | 基于LLM或自定义函数动态选择下一个发言者的群聊管理器 |
| Swarm团队 | `autogen_agentchat.teams.Swarm` | 支持智能体间动态切换和协作的团队模式 |
| 基础团队类 | `autogen_agentchat.base.Team` | 所有团队类型的基类 |

### **模型客户端 (Model Clients)**
| 组件/概念 | 核心实现类 | 职责与作用 |
| :--- | :--- | :--- |
| OpenAI客户端 | `autogen_ext.models.openai.OpenAIChatCompletionClient` | 连接OpenAI API的模型客户端 |
| Azure OpenAI客户端 | `autogen_ext.models.openai.AzureOpenAIChatCompletionClient` | 连接Azure OpenAI服务的模型客户端 |
| 模型缓存 | `autogen_ext.models.cache.ChatCompletionCache` | 为模型客户端提供缓存功能的包装器 |
| 基础模型客户端 | `autogen_core.models.ChatCompletionClient` | 所有模型客户端的基类和通用接口 |

### **代码执行器 (Code Executors)**
| 组件/概念 | 核心实现类 | 职责与作用 |
| :--- | :--- | :--- |
| Docker执行器 | `autogen_ext.code_executors.docker.DockerCommandLineCodeExecutor` | 在Docker容器中安全执行代码 |
| 本地执行器 | `autogen_ext.code_executors.local.LocalCommandLineCodeExecutor` | 在本地环境中执行代码（仅用于开发） |
| ACA动态会话执行器 | `autogen_ext.code_executors.aca_dynamic_sessions.ACADynamicSessionsCodeExecutor` | 使用Azure Container Apps动态会话执行代码 |
| Jupyter执行器 | `autogen_ext.code_executors.jupyter.JupyterCodeExecutor` | 在Jupyter环境中执行代码 |

### **工具与扩展 (Tools & Extensions)**
| 组件/概念 | 核心实现类 | 职责与作用 |
| :--- | :--- | :--- |
| MCP工具集成 | `autogen_ext.tools.mcp.McpWorkbench` | 支持Model-Context Protocol (MCP)服务器集成 |
| 工具基类 | `autogen_agentchat.base.Tool` | 定义工具接口的基类 |
| 函数工具 | `autogen_core.tools.FunctionTool` | 将Python函数包装为工具的实现 |

### **消息类型 (Message Types)**
| 组件/概念 | 核心实现类 | 职责与作用 |
| :--- | :--- | :--- |
| 文本消息 | `autogen_agentchat.messages.TextMessage` | 纯文本消息 |
| 多模态消息 | `autogen_agentchat.messages.MultiModalMessage` | 包含文本、图像等多种内容的消息 |
| 工具调用请求事件 | `autogen_agentchat.messages.ToolCallRequestEvent` | 工具调用请求消息 |
| 工具调用执行事件 | `autogen_agentchat.messages.ToolCallExecutionEvent` | 工具调用执行结果消息 |
| 停止消息 | `autogen_agentchat.messages.StopMessage` | 表示对话结束的消息 |
| 切换消息 | `autogen_agentchat.messages.HandoffMessage` | 智能体间切换控制权的消息 |
| 工具调用摘要消息 | `autogen_agentchat.messages.ToolCallSummaryMessage` | 工具调用结果的摘要消息 |

### **终止条件 (Termination Conditions)**
| 组件/概念 | 核心实现类 | 职责与作用 |
| :--- | :--- | :--- |
| 文本提及终止 | `autogen_agentchat.conditions.TextMentionTermination` | 当消息包含特定文本时终止对话 |
| 最大消息终止 | `autogen_agentchat.conditions.MaxMessageTermination` | 达到最大消息数时终止对话 |
| 最大轮次终止 | `autogen_agentchat.conditions.MaxTurnsTermination` | 达到最大轮次时终止对话 |
| 源匹配终止 | `autogen_agentchat.conditions.SourceMatchTermination` | 特定智能体发言时终止对话 |
| 组合终止条件 | `autogen_agentchat.conditions.CombinationTermination` | 组合多个终止条件（AND/OR逻辑） |

### **内存与上下文 (Memory & Context)**
| 组件/概念 | 核心实现类 | 职责与作用 |
| :--- | :--- | :--- |
| 内存基类 | `autogen_core.memory.Memory` | 智能体记忆存储的基类接口 |
| 缓冲上下文 | `autogen_core.model_context.BufferedChatCompletionContext` | 限制发送给模型的消息历史长度 |
| 聊天完成上下文 | `autogen_core.model_context.ChatCompletionContext` | 管理消息历史和提供虚拟视图的基类 |

### **缓存存储 (Cache Stores)**
| 组件/概念 | 核心实现类 | 职责与作用 |
| :--- | :--- | :--- |
| 磁盘缓存存储 | `autogen_ext.cache_store.diskcache.DiskCacheStore` | 基于磁盘的缓存存储实现 |
| Redis缓存存储 | `autogen_ext.cache_store.redis.RedisStore` | 基于Redis的缓存存储实现 |

### **运行时环境 (Runtime Environments)**
| 组件/概念 | 核心实现类 | 职责与作用 |
| :--- | :--- | :--- |
| gRPC工作运行时 | `autogen_ext.runtimes.grpc.GrpcWorkerAgentRuntime` | 支持分布式智能体部署的gRPC运行时环境 |
| 单线程运行时 | `autogen_core.SingleThreadedAgentRuntime` | 单线程智能体运行时环境 |

### **用户界面 (User Interfaces)**
| 组件/概念 | 核心实现类 | 职责与作用 |
| :--- | :--- | :--- |
| 控制台界面 | `autogen_agentchat.ui.Console` | 在控制台中显示对话流的简单UI |

---

## **第三部分：关键类API速查 (Key Class API Quick-Reference)**

### **`autogen_agentchat.agents.AssistantAgent`**
> **用途:** 基于LLM的智能体，负责文本生成、分析和推理任务。
> **关键初始化参数:**

| 参数 | 类型 | 描述 | 常用值/示例 |
| :--- | :--- | :--- | :--- |
| `name` | `str` | 智能体的唯一标识名称 | `"assistant"`, `"coder"`, `"analyst"` |
| `model_client` | `ChatCompletionClient` | LLM模型客户端实例 | `OpenAIChatCompletionClient(model="gpt-4o")` |
| `system_message` | `str` | 定义智能体角色和行为的系统提示 | `"You are a Python expert..."` |
| `tools` | `List[Tool]` | 智能体可使用的工具列表 | `[calculator_tool, web_search_tool]` |

### **`autogen_agentchat.agents.UserProxyAgent`**
> **用途:** 代表用户参与对话，支持人工输入和交互。
> **关键初始化参数:**

| 参数 | 类型 | 描述 | 常用值/示例 |
| :--- | :--- | :--- | :--- |
| `name` | `str` | 智能体的唯一标识名称 | `"user_proxy"`, `"human"` |
| `human_input_mode` | `str` | 控制何时需要人工输入 | `"NEVER"`, `"ALWAYS"`, `"TERMINATE"` |
| `max_consecutive_auto_reply` | `int` | 最大连续自动回复次数 | `10`, `20` |

### **`autogen_agentchat.agents.CodeExecutorAgent`**
> **用途:** 专门执行代码的智能体，提供安全的代码执行环境。
> **关键初始化参数:**

| 参数 | 类型 | 描述 | 常用值/示例 |
| :--- | :--- | :--- | :--- |
| `name` | `str` | 智能体的唯一标识名称 | `"code_executor"`, `"runner"` |
| `code_executor` | `CodeExecutor` | 代码执行器实例 | `DockerCommandLineCodeExecutor()` |
| `model_client` | `ChatCompletionClient` | 可选的LLM客户端用于结果分析 | `OpenAIChatCompletionClient(model="gpt-4o")` |

### **`autogen_agentchat.teams.RoundRobinGroupChat`**
> **用途:** 管理多智能体轮询式群聊对话。
> **关键初始化参数:**

| 参数 | 类型 | 描述 | 常用值/示例 |
| :--- | :--- | :--- | :--- |
| `participants` | `List[Agent]` | 参与群聊的智能体列表 | `[assistant, user_proxy, executor]` |
| `max_turns` | `int` | 最大对话轮次 | `10`, `20` |
| `termination_condition` | `TerminationCondition` | 终止条件 | `TextMentionTermination("TERMINATE")` |

### **`autogen_agentchat.teams.SelectorGroupChat`**
> **用途:** 基于LLM或自定义函数动态选择下一个发言者的群聊管理器。
> **关键初始化参数:**

| 参数 | 类型 | 描述 | 常用值/示例 |
| :--- | :--- | :--- | :--- |
| `participants` | `List[Agent]` | 参与群聊的智能体列表 | `[assistant, analyst, executor]` |
| `model_client` | `ChatCompletionClient` | 用于选择发言者的模型客户端 | `OpenAIChatCompletionClient(model="gpt-4o-mini")` |
| `termination_condition` | `TerminationCondition` | 终止条件 | `MaxMessageTermination(25)` |
| `selector_func` | `Callable` | 自定义选择器函数（可选） | `lambda messages: "next_speaker"` |

### **`autogen_ext.models.openai.OpenAIChatCompletionClient`**
> **用途:** 连接OpenAI API的模型客户端。
> **关键初始化参数:**

| 参数 | 类型 | 描述 | 常用值/示例 |
| :--- | :--- | :--- | :--- |
| `model` | `str` | 模型名称 | `"gpt-4o"`, `"gpt-4o-mini"`, `"gpt-3.5-turbo"` |
| `api_key` | `str` | OpenAI API密钥 | `"sk-xxx"` 或 `os.environ["OPENAI_API_KEY"]` |
| `base_url` | `str` | API基础URL（可选） | `"https://api.openai.com/v1"` |
| `temperature` | `float` | 生成温度 | `0.0` - `2.0` |
| `max_tokens` | `int` | 最大生成令牌数 | `1000`, `2000`, `4000` |
| `seed` | `int` | 随机种子（可选） | `42` |

### **`autogen_ext.models.openai.AzureOpenAIChatCompletionClient`**
> **用途:** 连接Azure OpenAI服务的模型客户端。
> **关键初始化参数:**

| 参数 | 类型 | 描述 | 常用值/示例 |
| :--- | :--- | :--- | :--- |
| `azure_deployment` | `str` | Azure部署名称 | `"gpt-4o"` |
| `azure_endpoint` | `str` | Azure端点URL | `"https://your-resource.openai.azure.com/"` |
| `api_key` | `str` | Azure API密钥 | `"your-azure-key"` |
| `api_version` | `str` | API版本 | `"2024-09-01-preview"` |
| `model` | `str` | 模型名称 | `"gpt-4o"` |

### **`autogen_ext.code_executors.docker.DockerCommandLineCodeExecutor`**
> **用途:** 在Docker容器中安全执行代码。
> **关键初始化参数:**

| 参数 | 类型 | 描述 | 常用值/示例 |
| :--- | :--- | :--- | :--- |
| `image` | `str` | Docker镜像名称 | `"python:3.11-slim"`, `"python:3.12"` |
| `work_dir` | `str` | 工作目录 | `"/tmp/autogen_code"` |
| `timeout` | `int` | 执行超时时间（秒） | `60`, `120` |
| `container_name` | `str` | 容器名称前缀（可选） | `"autogen_executor"` |

### **`autogen_ext.code_executors.local.LocalCommandLineCodeExecutor`**
> **用途:** 在本地环境中执行代码（仅用于开发测试）。
> **关键初始化参数:**

| 参数 | 类型 | 描述 | 常用值/示例 |
| :--- | :--- | :--- | :--- |
| `work_dir` | `str` | 工作目录 | `"./code_execution"` |
| `timeout` | `int` | 执行超时时间（秒） | `30`, `60` |

### **`autogen_agentchat.conditions.TextMentionTermination`**
> **用途:** 当消息包含特定文本时终止对话。
> **关键初始化参数:**

| 参数 | 类型 | 描述 | 常用值/示例 |
| :--- | :--- | :--- | :--- |
| `text` | `str` | 触发终止的文本 | `"TERMINATE"`, `"DONE"`, `"COMPLETE"` |

### **`autogen_agentchat.conditions.MaxMessageTermination`**
> **用途:** 达到最大消息数时终止对话。
> **关键初始化参数:**

| 参数 | 类型 | 描述 | 常用值/示例 |
| :--- | :--- | :--- | :--- |
| `max_messages` | `int` | 最大消息数 | `20`, `50`, `100` |

---

## **第四部分：核心工作流模式 (Core Workflow Patterns)**

### **模式1：双智能体对话与代码执行**
> **适用场景:** 最基础的模式。一个智能体生成代码，另一个智能体执行代码并返回结果。
> **参与者:** `AssistantAgent` (代码生成者), `CodeExecutorAgent` (代码执行者)。
> **工作流逻辑:**
> 1. 用户向 `AssistantAgent` 提出需要编程解决的任务
> 2. `AssistantAgent` 生成Python代码
> 3. `CodeExecutorAgent` 执行代码并返回结果
> 4. `AssistantAgent` 根据执行结果进行分析或迭代
> 5. 循环直到任务完成

> **[完整可运行代码]**
> ```python
> import asyncio
> from autogen_agentchat.agents import AssistantAgent, CodeExecutorAgent
> from autogen_ext.models.openai import OpenAIChatCompletionClient
> from autogen_ext.code_executors.docker import DockerCommandLineCodeExecutor
> 
> async def main():
>     # 1. 配置模型客户端
>     model_client = OpenAIChatCompletionClient(model="gpt-4o")
>     
>     # 2. 创建智能体
>     assistant = AssistantAgent(
>         name="coder",
>         model_client=model_client,
>         system_message="You are a Python expert. Write clean, efficient code to solve problems."
>     )
>     
>     code_executor = CodeExecutorAgent(
>         name="executor",
>         code_executor=DockerCommandLineCodeExecutor()
>     )
>     
>     # 3. 启动对话
>     result = await assistant.run(
>         task="Calculate the factorial of 10 and save the result to a file named 'factorial.txt'",
>         recipient=code_executor
>     )
>     print(result)
> 
> asyncio.run(main())
> ```

### **模式2：多智能体群聊协作**
> **适用场景:** 复杂任务需要多个专业智能体协作完成。
> **参与者:** 多个 `AssistantAgent` (不同专业角色), `UserProxyAgent` (用户代表), `RoundRobinGroupChat` (协调者)。
> **工作流逻辑:**
> 1. 用户通过 `UserProxyAgent` 发起任务
> 2. `RoundRobinGroupChat` 按顺序让各智能体发言
> 3. 每个智能体根据自己的专业领域提供输入
> 4. 智能体之间相互讨论和完善方案
> 5. 最终达成一致的解决方案

> **[完整可运行代码]**
> ```python
> import asyncio
> from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
> from autogen_agentchat.teams import RoundRobinGroupChat
> from autogen_ext.models.openai import OpenAIChatCompletionClient
> 
> async def main():
>     # 1. 配置模型客户端
>     model_client = OpenAIChatCompletionClient(model="gpt-4o")
>     
>     # 2. 创建专业智能体
>     architect = AssistantAgent(
>         name="architect",
>         model_client=model_client,
>         system_message="You are a software architect. Design system architecture and APIs."
>     )
>     
>     developer = AssistantAgent(
>         name="developer", 
>         model_client=model_client,
>         system_message="You are a senior developer. Implement code based on architecture."
>     )
>     
>     tester = AssistantAgent(
>         name="tester",
>         model_client=model_client,
>         system_message="You are a QA engineer. Design tests and identify potential issues."
>     )
>     
>     user_proxy = UserProxyAgent(name="user", human_input_mode="TERMINATE")
>     
>     # 3. 创建群聊
>     team = RoundRobinGroupChat(
>         participants=[architect, developer, tester, user_proxy],
>         max_turns=15
>     )
>     
>     # 4. 启动协作任务
>     result = await team.run(
>         task="Design and implement a REST API for a simple todo application with proper testing strategy"
>     )
>     print(result)
> 
> asyncio.run(main())
> ```

### **模式3：工具增强智能体**
> **适用场景:** 智能体需要调用外部工具或API来完成任务。
> **参与者:** `AssistantAgent` (配置工具), 各种工具函数。
> **工作流逻辑:**
> 1. 为智能体配置可用的工具集
> 2. 智能体根据任务需求选择合适的工具
> 3. 执行工具调用并获取结果
> 4. 基于工具结果继续推理或执行下一步

> **[完整可运行代码]**
> ```python
> import asyncio
> from autogen_agentchat.agents import AssistantAgent
> from autogen_ext.models.openai import OpenAIChatCompletionClient
> from autogen_agentchat.base import Tool
> import requests
> import json
>
> # 定义工具函数
> def web_search(query: str) -> str:
>     """搜索网络信息"""
>     # 这里是示例，实际应该调用真实的搜索API
>     return f"搜索结果：关于'{query}'的相关信息..."
>
> def calculate(expression: str) -> float:
>     """安全计算数学表达式"""
>     try:
>         # 注意：实际使用中应该使用更安全的计算方法
>         result = eval(expression)
>         return result
>     except:
>         return "计算错误"
>
> async def main():
>     # 1. 配置模型客户端
>     model_client = OpenAIChatCompletionClient(model="gpt-4o")
>
>     # 2. 定义工具
>     search_tool = Tool(
>         name="web_search",
>         description="搜索网络信息",
>         func=web_search
>     )
>
>     calc_tool = Tool(
>         name="calculator",
>         description="计算数学表达式",
>         func=calculate
>     )
>
>     # 3. 创建配置工具的智能体
>     assistant = AssistantAgent(
>         name="research_assistant",
>         model_client=model_client,
>         system_message="You are a research assistant with access to web search and calculator tools.",
>         tools=[search_tool, calc_tool]
>     )
>
>     # 4. 执行需要工具的任务
>     result = await assistant.run(
>         task="Search for the current population of Tokyo and calculate what 15% of that number would be"
>     )
>     print(result)
>
> asyncio.run(main())
> ```

### **模式4：流式对话与实时交互**
> **适用场景:** 需要实时显示智能体对话过程，支持中途干预。
> **参与者:** 任意智能体组合，配置流式输出。
> **工作流逻辑:**
> 1. 启用流式消息输出
> 2. 实时显示智能体思考和对话过程
> 3. 支持用户中途介入和指导
> 4. 可暂停、恢复或重定向对话

> **[完整可运行代码]**
> ```python
> import asyncio
> from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
> from autogen_ext.models.openai import OpenAIChatCompletionClient
>
> async def streaming_conversation():
>     # 1. 配置模型客户端
>     model_client = OpenAIChatCompletionClient(model="gpt-4o")
>
>     # 2. 创建智能体
>     assistant = AssistantAgent(
>         name="assistant",
>         model_client=model_client,
>         system_message="You are a helpful assistant."
>     )
>
>     user_proxy = UserProxyAgent(
>         name="user",
>         human_input_mode="ALWAYS"  # 允许实时人工输入
>     )
>
>     # 3. 启动流式对话
>     async for message in assistant.run_stream(
>         task="Help me plan a weekend trip to a nearby city",
>         recipient=user_proxy
>     ):
>         print(f"[{message.sender}]: {message.content}")
>
>         # 可以在这里添加中途干预逻辑
>         if "budget" in message.content.lower():
>             print("💡 提示：您可以随时调整预算要求")
>
> asyncio.run(streaming_conversation())
> ```

### **模式5：Swarm智能体协作**
> **适用场景:** 智能体之间需要动态切换控制权，支持复杂的协作流程。
> **参与者:** 多个 `AssistantAgent`，使用 `Swarm` 团队管理。
> **工作流逻辑:**
> 1. 智能体可以通过HandoffMessage将控制权转移给其他智能体
> 2. 支持动态的工作流程和智能体间协作
> 3. 每个智能体可以决定何时以及向谁转移控制权

> **[完整可运行代码]**
> ```python
> import asyncio
> from autogen_agentchat.agents import AssistantAgent
> from autogen_agentchat.teams import Swarm
> from autogen_agentchat.conditions import MaxMessageTermination
> from autogen_agentchat.ui import Console
> from autogen_ext.models.openai import OpenAIChatCompletionClient
>
> async def main():
>     # 1. 配置模型客户端
>     model_client = OpenAIChatCompletionClient(model="gpt-4o")
>
>     # 2. 创建专业智能体
>     researcher = AssistantAgent(
>         name="researcher",
>         model_client=model_client,
>         system_message="""You are a researcher. Research the topic and when done,
>         hand off to the writer by saying 'HANDOFF writer'."""
>     )
>
>     writer = AssistantAgent(
>         name="writer",
>         model_client=model_client,
>         system_message="""You are a writer. Write content based on research.
>         When done, hand off to the editor by saying 'HANDOFF editor'."""
>     )
>
>     editor = AssistantAgent(
>         name="editor",
>         model_client=model_client,
>         system_message="""You are an editor. Review and improve the content.
>         When satisfied, say 'TERMINATE'."""
>     )
>
>     # 3. 创建Swarm团队
>     swarm = Swarm(
>         agents=[researcher, writer, editor],
>         termination_condition=MaxMessageTermination(20)
>     )
>
>     # 4. 启动协作任务
>     result = await swarm.run(
>         task="Create a comprehensive article about the benefits of renewable energy"
>     )
>     await Console(swarm.run_stream(task="Create a comprehensive article about renewable energy"))
>
>     await model_client.close()
>
> asyncio.run(main())
> ```

### **模式6：嵌套智能体团队**
> **适用场景:** 需要层次化的智能体结构，内部团队专门处理特定子任务。
> **参与者:** `SocietyOfMindAgent` 包含内部团队，与其他智能体协作。
> **工作流逻辑:**
> 1. 外层智能体接收任务
> 2. 内部团队协作完成子任务
> 3. 将结果返回给外层对话

> **[完整可运行代码]**
> ```python
> import asyncio
> from autogen_agentchat.agents import AssistantAgent, SocietyOfMindAgent
> from autogen_agentchat.teams import RoundRobinGroupChat
> from autogen_agentchat.conditions import TextMentionTermination
> from autogen_agentchat.ui import Console
> from autogen_ext.models.openai import OpenAIChatCompletionClient
>
> async def main():
>     # 1. 配置模型客户端
>     model_client = OpenAIChatCompletionClient(model="gpt-4o")
>
>     # 2. 创建内部团队智能体
>     data_analyst = AssistantAgent(
>         name="data_analyst",
>         model_client=model_client,
>         system_message="You are a data analyst. Analyze data and provide insights."
>     )
>
>     statistician = AssistantAgent(
>         name="statistician",
>         model_client=model_client,
>         system_message="You are a statistician. Perform statistical analysis."
>     )
>
>     # 3. 创建内部分析团队
>     analysis_team = RoundRobinGroupChat(
>         participants=[data_analyst, statistician],
>         termination_condition=TextMentionTermination("ANALYSIS_COMPLETE"),
>         max_turns=10
>     )
>
>     # 4. 创建嵌套智能体
>     analysis_agent = SocietyOfMindAgent(
>         name="analysis_team_agent",
>         team=analysis_team,
>         model_client=model_client,
>         description="A team of analysts that can perform comprehensive data analysis"
>     )
>
>     # 5. 创建主报告智能体
>     report_writer = AssistantAgent(
>         name="report_writer",
>         model_client=model_client,
>         system_message="You write comprehensive reports based on analysis results."
>     )
>
>     # 6. 创建主团队
>     main_team = RoundRobinGroupChat(
>         participants=[analysis_agent, report_writer],
>         termination_condition=TextMentionTermination("REPORT_COMPLETE"),
>         max_turns=8
>     )
>
>     # 7. 运行嵌套团队任务
>     await Console(main_team.run_stream(
>         task="Analyze the quarterly sales data and create a comprehensive report"
>     ))
>
>     await model_client.close()
>
> asyncio.run(main())
> ```

---

## **第五部分：高级配置与扩展 (Advanced Configuration & Extensions)**

### **模型客户端配置**
```python
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.models.azure import AzureOpenAIChatCompletionClient

# OpenAI配置
openai_client = OpenAIChatCompletionClient(
    model="gpt-4o",
    api_key="your-api-key",
    temperature=0.7,
    max_tokens=2000
)

# Azure OpenAI配置
azure_client = AzureOpenAIChatCompletionClient(
    model="gpt-4",
    azure_endpoint="https://your-resource.openai.azure.com/",
    api_key="your-azure-key",
    api_version="2024-02-01"
)
```

### **代码执行环境配置**
```python
from autogen_ext.code_executors.docker import DockerCommandLineCodeExecutor
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor

# Docker执行器（推荐用于生产环境）
docker_executor = DockerCommandLineCodeExecutor(
    image="python:3.11-slim",
    work_dir="/tmp/autogen_code",
    timeout=60
)

# 本地执行器（仅用于开发测试）
local_executor = LocalCommandLineCodeExecutor(
    work_dir="./code_execution",
    timeout=30
)
```

### **观测性与监控配置**
```python
import logging
from autogen_agentchat.base import MessageTracker

# 配置日志
logging.basicConfig(level=logging.INFO)

# 消息追踪
tracker = MessageTracker()

# 在智能体中启用追踪
assistant = AssistantAgent(
    name="tracked_assistant",
    model_client=model_client,
    message_tracker=tracker
)
```

### **扩展与插件系统**
```python
from autogen_ext.tools.mcp import McpWorkbench
from autogen_ext.agents.openai import OpenAIAssistantAgent

# MCP工具集成
mcp_tools = McpWorkbench(
    server_config={
        "command": "npx",
        "args": ["@modelcontextprotocol/server-filesystem", "/path/to/files"]
    }
)

# OpenAI Assistant API集成
openai_assistant = OpenAIAssistantAgent(
    name="openai_assistant",
    assistant_id="asst_xxx",  # 预创建的Assistant ID
    api_key="your-openai-key"
)
```

### **终止条件配置**
```python
from autogen_agentchat.conditions import (
    TextMentionTermination,
    MaxMessageTermination,
    MaxTurnsTermination,
    SourceMatchTermination,
    CombinationTermination
)

# 文本终止条件
text_termination = TextMentionTermination("TERMINATE")

# 最大消息数终止
max_msg_termination = MaxMessageTermination(max_messages=50)

# 最大轮次终止
max_turns_termination = MaxTurnsTermination(max_turns=10)

# 特定智能体终止
source_termination = SourceMatchTermination(source="user_proxy")

# 组合终止条件（OR逻辑）
combined_termination = text_termination | max_msg_termination

# 组合终止条件（AND逻辑）
strict_termination = text_termination & max_turns_termination
```

### **消息类型处理**
```python
from autogen_agentchat.messages import (
    TextMessage,
    MultiModalMessage,
    StopMessage,
    HandoffMessage
)
from autogen_core import Image

# 创建文本消息
text_msg = TextMessage(content="Hello, world!", source="user")

# 创建多模态消息
multimodal_msg = MultiModalMessage(
    content=["Analyze this image:", Image.from_file("chart.png")],
    source="user"
)

# 创建停止消息
stop_msg = StopMessage(content="Task completed", source="assistant")

# 创建切换消息（用于Swarm）
handoff_msg = HandoffMessage(
    content="Transferring to writer",
    target="writer",
    source="researcher"
)
```

### **内存与上下文管理**
```python
from autogen_core.model_context import BufferedChatCompletionContext
from autogen_core.memory import Memory

# 缓冲上下文 - 限制消息历史
buffered_context = BufferedChatCompletionContext(buffer_size=20)

# 在智能体中使用上下文管理
assistant_with_context = AssistantAgent(
    name="context_assistant",
    model_client=model_client,
    model_context=buffered_context,  # 只保留最近20条消息
    system_message="You are a helpful assistant with limited memory."
)

# 自定义内存实现示例
class SimpleMemory(Memory):
    def __init__(self):
        self._memories = []

    async def add(self, content: str) -> None:
        self._memories.append(content)

    async def get(self, query: str) -> List[str]:
        # 简单的关键词匹配
        return [mem for mem in self._memories if query.lower() in mem.lower()]

# 在智能体中使用内存
memory = SimpleMemory()
assistant_with_memory = AssistantAgent(
    name="memory_assistant",
    model_client=model_client,
    memory=[memory],
    system_message="You have access to memory for storing and retrieving information."
)
```

### **工具定义与使用**
```python
from autogen_agentchat.base import Tool
from autogen_core.tools import FunctionTool
import requests
import json

# 定义工具函数
def get_weather(city: str) -> str:
    """获取指定城市的天气信息"""
    # 这里应该调用真实的天气API
    return f"The weather in {city} is sunny with 25°C"

def calculate_math(expression: str) -> str:
    """安全地计算数学表达式"""
    try:
        # 注意：生产环境中应使用更安全的计算方法
        result = eval(expression)
        return f"Result: {result}"
    except Exception as e:
        return f"Error: {str(e)}"

# 创建工具
weather_tool = FunctionTool(get_weather, description="Get weather information for a city")
math_tool = FunctionTool(calculate_math, description="Calculate mathematical expressions")

# 为智能体配置工具
tool_agent = AssistantAgent(
    name="tool_agent",
    model_client=model_client,
    tools=[weather_tool, math_tool],
    system_message="You have access to weather and math tools. Use them to help users."
)
```

---

## **第六部分：最佳实践与生产部署 (Best Practices & Production Deployment)**

### **安全性最佳实践**
1. **代码执行隔离**: 始终使用Docker容器执行生成的代码
2. **API密钥管理**: 使用环境变量或密钥管理服务存储敏感信息
3. **输入验证**: 对用户输入进行严格验证和清理
4. **权限控制**: 限制智能体的系统访问权限

### **性能优化建议**
1. **异步处理**: 充分利用异步编程提高并发性能
2. **连接池**: 为模型客户端配置连接池
3. **缓存策略**: 缓存常用的模型响应和计算结果
4. **负载均衡**: 在多实例部署中实现负载均衡

### **监控与调试**
```python
# 启用详细日志
import logging
logging.getLogger("autogen").setLevel(logging.DEBUG)

# 性能监控
from autogen_agentchat.base import PerformanceMonitor

monitor = PerformanceMonitor()
assistant = AssistantAgent(
    name="monitored_assistant",
    model_client=model_client,
    performance_monitor=monitor
)
```

---

## **第七部分：行动指令 (Action Directives)**

> **[Instructions for AI]**
>
> ### **基础编程指令**
> 1. **严格遵循文档:** 当被要求使用AutoGen编程时，请严格遵循本文档中定义的类、参数和模式。
> 2. **优先使用模式:** 分析用户请求，选择"核心工作流模式"中最匹配的一个作为代码框架。
> 3. **精确使用参数:** 从"关键类API速查"中查找并使用正确的参数来配置智能体。
> 4. **异步优先:** AutoGen v0.4是异步框架，始终使用`async/await`语法和`asyncio.run(main())`。
>
> ### **架构选择指令**
> 5. **智能体选择:**
>    - 文本生成任务 → `AssistantAgent`
>    - 代码执行任务 → `CodeExecutorAgent`
>    - 用户交互任务 → `UserProxyAgent`
>    - 复杂嵌套任务 → `SocietyOfMindAgent`
>    - OpenAI Assistant API → `OpenAIAssistantAgent`
>
> 6. **团队模式选择:**
>    - 固定顺序协作 → `RoundRobinGroupChat`
>    - 动态选择发言者 → `SelectorGroupChat`
>    - 智能体间切换 → `Swarm`
>    - 简单双智能体 → 直接使用`assistant.run(recipient=other_agent)`
>
> 7. **模型客户端选择:**
>    - OpenAI API → `OpenAIChatCompletionClient`
>    - Azure OpenAI → `AzureOpenAIChatCompletionClient`
>    - 需要缓存 → 用`ChatCompletionCache`包装
>    - 自定义API → 设置`base_url`和`model_info`
>
> ### **安全与最佳实践**
> 8. **代码执行安全:**
>    - 生产环境必须使用`DockerCommandLineCodeExecutor`
>    - 开发测试可用`LocalCommandLineCodeExecutor`
>    - 设置合理的`timeout`参数
>    - 限制工作目录权限
>
> 9. **错误处理与监控:**
>    - 添加`try/except`块处理异常
>    - 使用`CancellationToken`支持任务取消
>    - 启用日志记录：`logging.getLogger("autogen").setLevel(logging.DEBUG)`
>    - 在生产环境中配置性能监控
>
> 10. **资源管理:**
>     - 始终调用`await model_client.close()`关闭连接
>     - 使用上下文管理器或finally块确保资源清理
>     - 合理设置`max_turns`和`max_messages`避免无限循环
>
> ### **组合与扩展指令**
> 11. **工具集成:**
>     - 定义工具函数时添加详细的docstring
>     - 使用`FunctionTool`包装Python函数
>     - 为智能体配置`tools`参数
>     - 考虑工具调用的安全性和权限控制
>
> 12. **终止条件设计:**
>     - 简单任务用`TextMentionTermination("TERMINATE")`
>     - 长对话用`MaxMessageTermination(50)`
>     - 复杂场景用组合条件`condition1 | condition2`
>     - 关键任务用严格条件`condition1 & condition2`
>
> 13. **内存与上下文:**
>     - 长对话使用`BufferedChatCompletionContext`限制上下文
>     - 需要记忆的场景实现自定义`Memory`类
>     - 考虑消息历史的存储和检索策略
>
> ### **调试与开发指令**
> 14. **开发调试:**
>     - 使用`Console(stream)`显示实时对话流
>     - 启用详细日志查看内部状态
>     - 使用`save_state`和`load_state`进行状态管理
>     - 测试时使用较小的模型（如gpt-4o-mini）降低成本
>
> 15. **生产部署:**
>     - 配置环境变量管理API密钥
>     - 使用连接池和缓存优化性能
>     - 实现健康检查和故障恢复机制
>     - 监控令牌使用量和成本

---

## **第八部分：常见问题与故障排除 (FAQ & Troubleshooting)**

### **常见问题**

**Q: 如何从AutoGen v0.2迁移到v0.4？**
A: 主要变化包括：
- 异步API：所有方法都是异步的，需要使用`async/await`
- 新的包结构：`autogen_agentchat`、`autogen_ext`、`autogen_core`
- 事件驱动架构：智能体通过异步消息通信
- 参考官方迁移指南进行逐步迁移

**Q: 智能体对话卡住不动怎么办？**
A: 检查以下几点：
- 设置合理的`max_turns`和`max_messages`
- 配置适当的终止条件
- 验证模型客户端配置和网络连接
- 检查API密钥和配额限制
- 启用日志查看详细错误信息

**Q: 如何处理代码执行安全问题？**
A: 安全最佳实践：
- 生产环境必须使用`DockerCommandLineCodeExecutor`
- 设置执行超时时间
- 限制工作目录权限和文件系统访问
- 禁用网络访问（如果不需要）
- 定期更新Docker镜像

**Q: 多智能体性能如何优化？**
A: 性能优化策略：
- 使用异步并发处理
- 配置模型客户端连接池
- 实现智能体响应缓存
- 使用较小的模型进行选择器功能
- 合理设置上下文窗口大小

**Q: 如何处理长对话的上下文溢出？**
A: 上下文管理方案：
- 使用`BufferedChatCompletionContext`限制消息历史
- 实现自定义的消息摘要策略
- 使用`Memory`类存储重要信息
- 定期保存和恢复对话状态

**Q: 工具调用失败怎么办？**
A: 工具调用故障排除：
- 检查工具函数的参数类型和返回值
- 确保工具函数有详细的docstring
- 验证工具权限和网络访问
- 使用`reflect_on_tool_use`参数让模型反思工具使用
- 添加工具调用的错误处理逻辑

**Q: 如何实现智能体的状态持久化？**
A: 状态管理方法：
- 使用`save_state()`和`load_state()`方法
- 将状态序列化为JSON格式存储
- 实现自定义的状态存储后端
- 考虑分布式环境下的状态同步

**Q: 模型调用成本如何控制？**
A: 成本控制策略：
- 使用较小的模型（如gpt-4o-mini）进行简单任务
- 启用模型响应缓存
- 设置合理的`max_tokens`限制
- 监控和分析令牌使用情况
- 实现智能的上下文截断策略

**Q: 如何调试复杂的多智能体交互？**
A: 调试技巧：
- 使用`Console(stream)`实时查看对话流
- 启用详细日志：`logging.getLogger("autogen").setLevel(logging.DEBUG)`
- 使用`run_stream`方法观察内部消息
- 实现自定义的消息追踪器
- 分步测试每个智能体的功能

**Q: 分布式部署有什么注意事项？**
A: 分布式部署要点：
- 使用`GrpcWorkerAgentRuntime`进行跨网络通信
- 配置负载均衡和故障恢复
- 实现健康检查机制
- 考虑网络延迟和超时设置
- 确保API密钥和配置的安全传输

### **故障排除清单**

#### **环境和配置检查**
- [ ] 检查Python版本（需要3.10+）
- [ ] 验证AutoGen包安装：`pip list | grep autogen`
- [ ] 检查API密钥配置和环境变量
- [ ] 验证模型名称和可用性
- [ ] 测试网络连接和防火墙设置
- [ ] 确认Docker环境正常运行（如果使用代码执行）

#### **基础功能测试**
- [ ] 测试简单的模型客户端调用
- [ ] 验证单个智能体的基本功能
- [ ] 测试简单的双智能体对话
- [ ] 检查工具函数的独立运行
- [ ] 验证消息序列化和反序列化

#### **日志和调试**
- [ ] 启用详细日志：`logging.getLogger("autogen").setLevel(logging.DEBUG)`
- [ ] 查看控制台输出和错误信息
- [ ] 检查异步任务的异常处理
- [ ] 使用`Console(stream)`观察实时对话流
- [ ] 验证`CancellationToken`的正确使用

#### **性能和资源**
- [ ] 监控内存使用情况
- [ ] 检查API调用频率和配额
- [ ] 验证超时设置的合理性
- [ ] 测试并发处理能力
- [ ] 确认资源正确释放（`await model_client.close()`）

#### **高级功能验证**
- [ ] 测试状态保存和恢复功能
- [ ] 验证终止条件的触发逻辑
- [ ] 检查工具调用的权限和安全性
- [ ] 测试分布式部署的连通性
- [ ] 验证缓存机制的有效性

#### **生产环境检查**
- [ ] 确认安全配置（Docker隔离、权限控制）
- [ ] 验证监控和告警机制
- [ ] 测试故障恢复和重试逻辑
- [ ] 检查负载均衡配置
- [ ] 确认备份和恢复策略

---

**文档版本:** v0.4.0
**最后更新:** 2025年1月
**维护者:** Microsoft AutoGen Team
