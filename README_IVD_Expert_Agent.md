# IVD领域专家Agent

## 项目概述

本项目基于prompt_manager框架创建了一个专业的体外诊断（IVD）领域专家agent。该agent具有深度的专业知识、数据思维和权威的分析能力，能够为IVD相关的技术、法规、市场和临床问题提供专业咨询。

## 核心特性

### 🧬 专业深度
- **15年+行业经验**：涵盖分子诊断、免疫诊断、生化诊断等多个技术平台
- **全面知识体系**：技术原理、法规标准、市场分析、临床应用四大知识模块
- **权威数据支撑**：基于FDA、NMPA、ISO标准等权威来源的最新信息

### 📊 数据思维
- **定量分析优先**：提供具体的敏感性、特异性、PPV、NPV等性能指标
- **统计学严谨性**：关注样本量、置信区间、p值等统计参数
- **成本效益评估**：运用QALY、ICER等卫生经济学指标

### 🎯 专业表达
- **精准术语使用**：LoD、LoQ、CV、Bias等专业缩写
- **规范引用格式**：温哥华格式，提供DOI和PubMed ID
- **多维度分析**：技术、法规、市场、临床综合评估

## 文件结构

```
expert_agent/
├── create_ivd_expert_agent.py      # 创建agent的主脚本
├── test_ivd_expert_agent.py        # 测试agent功能
├── use_ivd_expert_example.py       # 使用示例和演示
├── ivd_expert_system_prompt.md     # 系统提示词模板
├── ivd_expert_user_prompt.md       # 用户提示词模板
├── ivd_technical_knowledge.md      # 技术知识库
├── ivd_regulatory_knowledge.md     # 法规知识库
└── ivd_prompt_store/               # 生成的agent存储目录
    └── ivd_expert/
        ├── config.json             # 配置文件
        ├── system_message.md       # 系统消息
        ├── user_message.md         # 用户消息模板
        ├── technical_knowledge.md  # 技术知识模块
        ├── regulatory_knowledge.md # 法规知识模块
        ├── market_knowledge.md     # 市场知识模块
        └── clinical_knowledge.md   # 临床知识模块
```

## 知识模块详解

### 1. 技术知识库 (technical_knowledge.md)
- **分子诊断技术**：PCR、qPCR、dPCR、NGS、LAMP等
- **免疫诊断技术**：ELISA、CLIA、ECLIA、LFIA等
- **生化诊断技术**：酶学检测、电解质分析等
- **POCT技术**：血气分析、血糖检测等
- **新兴技术**：CRISPR诊断、液体活检、AI辅助诊断

### 2. 法规知识库 (regulatory_knowledge.md)
- **国际法规**：FDA 510(k)/PMA、CE-IVD、IVDR等
- **中国法规**：NMPA三类医疗器械注册流程
- **质量标准**：ISO 13485、ISO 15189、ISO 14971
- **CLSI指南**：EP系列、M系列技术文件
- **临床试验**：设计要点、统计分析要求

### 3. 市场知识库 (market_knowledge.md)
- **全球市场**：规模$102.1亿美元，CAGR 6.1%
- **竞争格局**：Roche、Abbott、Siemens等头部企业
- **技术趋势**：NGS成本下降、POCT增长、液体活检
- **投资并购**：2023年重大交易和融资情况

### 4. 临床知识库 (clinical_knowledge.md)
- **疾病诊断**：感染性疾病、心血管疾病、肿瘤标志物
- **个性化医疗**：药物基因组学、肿瘤精准治疗
- **质量控制**：Westgard规则、室间质评
- **临床决策**：参考区间、敏感性特异性解释

## 快速开始

### 1. 创建Agent

```bash
python create_ivd_expert_agent.py
```

这将创建完整的IVD专家agent，包含所有知识模块。

### 2. 测试Agent

```bash
python test_ivd_expert_agent.py
```

验证agent的配置、知识模块和提示词生成功能。

### 3. 使用Agent

```bash
python use_ivd_expert_example.py
```

运行交互式演示，体验不同类型的专业咨询。

## 使用方法

### Python API使用

```python
from prompt_manager import PromptManager

# 初始化
pm = PromptManager("./ivd_prompt_store")
prompts = pm.get_prompts("ivd_expert")

# 生成提示词
system_prompt = prompts['system'].format()
user_prompt = prompts['user'].format(
    question_type="技术分析",
    user_question="请分析qPCR和dPCR的技术差异",
    context_info="用于液体活检产品开发"
)

# 发送给AI模型
# response = ai_model.chat(system_prompt, user_prompt)
```

### 支持的咨询类型

1. **技术分析**：技术原理、性能参数、优缺点对比
2. **法规合规性**：注册流程、技术要求、风险评估
3. **市场和商业分析**：市场规模、竞争格局、投资机会
4. **临床应用价值**：诊断性能、成本效益、实施挑战

## 应用场景

### 🏢 企业应用
- **产品开发**：技术选型、性能优化建议
- **法规事务**：注册策略、合规风险评估
- **市场分析**：竞争情报、商业机会识别
- **临床转化**：临床试验设计、性能评估

### 🎓 学术研究
- **文献调研**：技术发展趋势分析
- **项目申请**：技术路线论证
- **论文写作**：专业术语和数据支撑
- **学术交流**：专业观点和见解

### 💼 投资决策
- **技术评估**：创新性和可行性分析
- **市场调研**：规模预测和增长驱动
- **风险评估**：技术和法规风险
- **价值评估**：商业模式和盈利能力

## 技术特点

### 🔧 基于prompt_manager框架
- **模块化设计**：知识库独立管理，易于更新
- **模板化生成**：支持变量替换和动态内容
- **版本控制**：配置文件管理，支持agent重载

### 📚 丰富的知识库
- **4个核心模块**：技术、法规、市场、临床
- **6000+字符**：深度专业内容
- **权威来源**：基于FDA、ISO、顶级期刊等

### 🎯 专业化设计
- **角色定位**：15年经验的资深专家
- **思维框架**：数据驱动、循证医学、风险评估
- **沟通风格**：专业术语、规范引用、深度分析

## 扩展和定制

### 添加新的知识模块

```python
# 在create_ivd_expert_agent.py中添加
new_knowledge = """# 新知识模块内容"""

pm.register_new_agent(
    agent_name="ivd_expert",
    system_message=system_message,
    user_message=user_message,
    # 现有知识模块...
    new_module=new_knowledge  # 新增模块
)
```

### 更新现有知识

```python
# 重新加载agent
pm.reload_agent("ivd_expert")

# 或删除后重新创建
pm.delete_agent("ivd_expert")
# 然后重新注册...
```

## 最佳实践

### 1. 问题描述
- **具体明确**：提供详细的技术参数和应用场景
- **背景信息**：包含公司情况、目标市场、技术要求
- **期望输出**：明确需要什么类型的分析和建议

### 2. 结果验证
- **交叉验证**：对比多个信息源
- **专家审核**：请领域专家验证关键结论
- **持续更新**：定期更新知识库内容

### 3. 集成应用
- **API封装**：将功能封装为REST API
- **批量处理**：支持多个问题同时处理
- **结果缓存**：避免重复生成相同提示词

## 注意事项

⚠️ **重要提醒**：
- 本agent生成的是专业提示词，需要发送给AI模型才能获得最终答案
- 建议使用GPT-4、Claude-3等高性能模型以获得最佳效果
- 对于关键决策，请结合实际情况和专家意见进行综合判断
- 定期更新知识库以保持信息的时效性和准确性

## 联系和支持

如有问题或建议，请通过以下方式联系：
- 技术问题：检查prompt_manager文档
- 内容更新：参考最新的FDA、NMPA指导原则
- 功能扩展：基于实际需求进行模块化开发

---

**版本信息**：v1.0  
**创建日期**：2024年  
**适用范围**：IVD行业专业人士、研究人员、投资者
