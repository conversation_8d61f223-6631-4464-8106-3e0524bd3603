#!/usr/bin/env python3
"""
IVD专家和评价专家Agent使用示例

这个脚本展示如何在实际工作中使用两个agent：
1. IVD专家agent - 生成专业分析
2. IVD评价专家agent - 评估分析质量
"""

import sys
from pathlib import Path

# 添加prompt_manager到Python路径
sys.path.append(str(Path(__file__).parent / "prompt_manager"))

from prompt_manager import PromptManager


class IVDAnalysisWorkflow:
    """IVD分析工作流程管理类"""
    
    def __init__(self, prompt_store_path="./ivd_prompt_store"):
        """初始化工作流程"""
        self.pm = PromptManager(prompt_store_path)
        self.expert_prompts = self.pm.get_prompts("ivd_expert")
        self.evaluator_prompts = self.pm.get_prompts("ivd_evaluator")
        
    def step1_generate_analysis(self, question_type, user_question, context_info=""):
        """第一步：生成专业分析提示词"""
        
        print("🔬 第一步：生成IVD专家分析提示词")
        print("="*60)
        
        system_prompt = self.expert_prompts['system'].format()
        user_prompt = self.expert_prompts['user'].format(
            question_type=question_type,
            user_question=user_question,
            context_info=context_info
        )
        
        print(f"✅ 专家分析提示词已生成")
        print(f"   系统提示词长度: {len(system_prompt)} 字符")
        print(f"   用户提示词长度: {len(user_prompt)} 字符")
        print(f"   问题类型: {question_type}")
        
        return system_prompt, user_prompt
    
    def step2_evaluate_analysis(self, original_question_type, original_question, 
                               original_context, analysis_content):
        """第二步：生成分析评价提示词"""
        
        print("\n📊 第二步：生成评价专家评价提示词")
        print("="*60)
        
        system_prompt = self.evaluator_prompts['system'].format()
        user_prompt = self.evaluator_prompts['user'].format(
            original_question_type=original_question_type,
            original_question=original_question,
            original_context=original_context,
            analysis_content=analysis_content
        )
        
        print(f"✅ 评价提示词已生成")
        print(f"   系统提示词长度: {len(system_prompt)} 字符")
        print(f"   用户提示词长度: {len(user_prompt)} 字符")
        print(f"   待评价内容长度: {len(analysis_content)} 字符")
        
        return system_prompt, user_prompt
    
    def complete_workflow(self, question_type, user_question, context_info="", 
                         analysis_content=None):
        """完整的分析和评价工作流程"""
        
        print("🚀 IVD分析质量控制完整工作流程")
        print("="*80)
        
        # 第一步：生成专家分析
        expert_system, expert_user = self.step1_generate_analysis(
            question_type, user_question, context_info
        )
        
        # 如果没有提供分析内容，提示用户
        if not analysis_content:
            print("\n⚠️  请将上述专家分析提示词发送给AI模型获得分析结果")
            print("   然后将分析结果作为analysis_content参数传入")
            return expert_system, expert_user, None, None
        
        # 第二步：生成评价
        eval_system, eval_user = self.step2_evaluate_analysis(
            question_type, user_question, context_info, analysis_content
        )
        
        print("\n🎯 工作流程完成！")
        print("   1. 专家分析提示词已生成")
        print("   2. 评价提示词已生成")
        print("   3. 可以发送给AI模型获得质量评价")
        
        return expert_system, expert_user, eval_system, eval_user


def demo_technical_analysis():
    """演示技术分析工作流程"""
    
    print("\n" + "="*80)
    print("演示1：技术分析工作流程")
    print("="*80)
    
    workflow = IVDAnalysisWorkflow()
    
    question_type = "技术分析"
    user_question = """
    请详细分析NGS和qPCR技术在肿瘤基因检测中的应用对比，
    包括检测原理、性能特点、成本分析和适用场景。
    """
    
    context_info = """
    我们医院病理科正在建设分子病理实验室，需要选择合适的基因检测技术平台。
    主要检测需求：
    1. 肿瘤驱动基因突变检测
    2. 药物敏感性相关基因检测  
    3. 预计年检测量3000-5000例
    4. 预算限制：设备投入<500万元
    """
    
    # 生成专家分析提示词
    expert_system, expert_user = workflow.step1_generate_analysis(
        question_type, user_question, context_info
    )
    
    print("\n📋 生成的专家分析提示词（用户部分）:")
    print("-"*60)
    print(expert_user[:800] + "..." if len(expert_user) > 800 else expert_user)
    
    print("\n💡 下一步操作:")
    print("1. 复制上述提示词发送给AI模型（如GPT-4、Claude等）")
    print("2. 获得专业的技术分析结果")
    print("3. 将分析结果用于第二步的质量评价")


def demo_evaluation_workflow():
    """演示评价工作流程"""
    
    print("\n" + "="*80)
    print("演示2：分析评价工作流程")
    print("="*80)
    
    workflow = IVDAnalysisWorkflow()
    
    # 模拟一个分析结果
    sample_analysis = """
## 执行摘要
NGS技术适合多基因检测，qPCR适合单基因快速检测。建议医院根据检测需求和预算选择NGS作为主要平台，qPCR作为补充。

## 技术对比分析

### NGS技术特点
- **检测原理**：大规模并行测序，可同时检测数百个基因
- **检测通量**：高，单次可检测96-384个样本
- **检测范围**：广，可检测点突变、插入缺失、拷贝数变异等
- **成本分析**：设备成本300-400万元，单样本成本800-1200元
- **检测周期**：3-5个工作日

### qPCR技术特点
- **检测原理**：实时荧光PCR，针对特定基因位点
- **检测通量**：中等，单次可检测96个样本
- **检测范围**：窄，主要检测已知热点突变
- **成本分析**：设备成本50-100万元，单样本成本200-400元
- **检测周期**：4-6小时

### 应用建议
基于医院年检测量3000-5000例和预算限制，建议：
1. 主要采用NGS平台进行多基因panel检测
2. 配置qPCR用于紧急样本和单基因验证
3. 分阶段实施，先建设NGS平台，后补充qPCR

### 参考文献
1. Gagan J, Van Allen EM. Next-generation sequencing to guide cancer therapy. Genome Med. 2015;7(1):80.
2. Luthra R, et al. Next-generation sequencing in clinical molecular diagnostics. Clin Chem. 2015;61(1):56-71.
    """
    
    # 生成评价提示词
    eval_system, eval_user = workflow.step2_evaluate_analysis(
        "技术分析",
        "请分析NGS和qPCR技术在肿瘤基因检测中的应用对比",
        "医院病理科建设分子病理实验室，年检测量3000-5000例，预算<500万元",
        sample_analysis
    )
    
    print("📊 分析评价提示词已生成")
    print(f"   待评价的分析内容长度: {len(sample_analysis)} 字符")
    print(f"   评价提示词长度: {len(eval_user)} 字符")
    
    print("\n📋 生成的评价提示词（部分预览）:")
    print("-"*60)
    print(eval_user[:600] + "..." if len(eval_user) > 600 else eval_user)
    
    print("\n💡 评价专家将从以下维度进行评价:")
    print("1. 科学严谨性 - 技术描述是否准确")
    print("2. 证据充分性 - 是否有充分的数据支撑")
    print("3. 客观中立性 - 是否平衡讨论优缺点")
    print("4. 完整性 - 是否全面覆盖关键方面")
    print("5. 实用价值 - 建议是否具体可操作")


def demo_complete_workflow():
    """演示完整工作流程"""
    
    print("\n" + "="*80)
    print("演示3：完整质量控制工作流程")
    print("="*80)
    
    workflow = IVDAnalysisWorkflow()
    
    # 完整工作流程示例
    question_type = "市场和商业分析"
    user_question = "请分析中国POCT市场的发展现状和投资机会"
    context_info = "投资机构评估POCT相关项目，关注技术创新和市场潜力"
    
    # 模拟的分析内容（实际使用中这是AI模型的输出）
    analysis_content = """
## 市场概况
中国POCT市场2023年规模约120亿元，预计2030年达到280亿元，年复合增长率约13%。

## 细分市场
- 血糖检测：占比35%，市场相对成熟
- 心血管标志物：占比20%，增长较快
- 感染性疾病：占比25%，受疫情推动
- 其他应用：占比20%

## 投资机会
1. 技术创新：微流控、生物传感器等新技术
2. 应用拓展：慢病管理、家庭医疗等新场景
3. 渠道下沉：基层医疗市场潜力巨大

## 主要风险
- 监管政策变化
- 技术替代风险
- 价格竞争激烈
    """
    
    # 执行完整工作流程
    expert_system, expert_user, eval_system, eval_user = workflow.complete_workflow(
        question_type, user_question, context_info, analysis_content
    )
    
    print("\n📊 工作流程结果:")
    print(f"   专家分析提示词: {len(expert_user)} 字符")
    print(f"   分析内容: {len(analysis_content)} 字符")
    print(f"   评价提示词: {len(eval_user)} 字符")
    
    print("\n🎯 质量控制闭环已形成:")
    print("   ✅ 专家分析 → ✅ 质量评价 → ✅ 改进建议")


def interactive_workflow():
    """交互式工作流程"""
    
    print("\n" + "="*80)
    print("交互式IVD分析工作流程")
    print("="*80)
    
    workflow = IVDAnalysisWorkflow()
    
    print("请选择分析类型:")
    print("1. 技术分析")
    print("2. 法规合规性")
    print("3. 市场和商业分析")
    print("4. 临床应用价值")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    question_types = {
        "1": "技术分析",
        "2": "法规合规性",
        "3": "市场和商业分析",
        "4": "临床应用价值"
    }
    
    if choice not in question_types:
        print("无效选择，使用默认类型：技术分析")
        choice = "1"
    
    question_type = question_types[choice]
    
    print(f"\n您选择了：{question_type}")
    print("\n请输入您的问题：")
    user_question = input().strip()
    
    print("\n请输入背景信息（可选，直接回车跳过）：")
    context_info = input().strip()
    
    if not user_question:
        print("未输入问题，使用演示问题...")
        user_question = "请分析IVD行业的发展趋势"
    
    # 生成专家分析提示词
    expert_system, expert_user = workflow.step1_generate_analysis(
        question_type, user_question, context_info
    )
    
    print("\n" + "="*60)
    print("专家分析提示词已生成！")
    print("请将以下提示词发送给AI模型：")
    print("="*60)
    print("\n【系统提示词】")
    print(expert_system[:500] + "..." if len(expert_system) > 500 else expert_system)
    print("\n【用户提示词】")
    print(expert_user)
    
    print("\n" + "="*60)
    print("获得分析结果后，可以使用评价专家进行质量评估")
    print("="*60)


def main():
    """主函数"""
    
    print("🧬 IVD专家和评价专家Agent使用指南")
    print("="*80)
    print("本指南展示如何使用两个专业agent进行IVD分析和质量控制")
    print("="*80)
    
    demos = [
        ("技术分析工作流程", demo_technical_analysis),
        ("分析评价工作流程", demo_evaluation_workflow),
        ("完整质量控制工作流程", demo_complete_workflow),
        ("交互式工作流程", interactive_workflow)
    ]
    
    print("\n可用的演示：")
    for i, (name, _) in enumerate(demos, 1):
        print(f"{i}. {name}")
    print(f"{len(demos)+1}. 运行所有演示")
    
    choice = input(f"\n请选择演示 (1-{len(demos)+1}): ").strip()
    
    if choice == str(len(demos)+1):
        for name, demo_func in demos[:-1]:  # 排除交互式演示
            demo_func()
    elif choice.isdigit() and 1 <= int(choice) <= len(demos):
        _, demo_func = demos[int(choice)-1]
        demo_func()
    else:
        print("无效选择，运行所有演示...")
        for name, demo_func in demos[:-1]:  # 排除交互式演示
            demo_func()
    
    print("\n" + "="*80)
    print("演示完成！")
    print("\n🔄 标准工作流程:")
    print("1. 使用IVD专家agent生成专业分析")
    print("2. 将分析结果发送给AI模型")
    print("3. 使用评价专家agent评估分析质量")
    print("4. 根据评价结果改进分析")
    print("5. 重复步骤3-4直到满意")
    print("\n💡 最佳实践:")
    print("- 提供详细的背景信息以获得更准确的分析")
    print("- 使用高性能AI模型（如GPT-4、Claude-3）")
    print("- 对关键决策进行多轮质量评价")
    print("- 结合专家意见进行最终验证")
    print("="*80)


if __name__ == "__main__":
    main()
